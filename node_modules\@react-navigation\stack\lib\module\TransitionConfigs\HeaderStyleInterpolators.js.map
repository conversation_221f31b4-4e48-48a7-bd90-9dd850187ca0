{"version": 3, "names": ["Animated", "Platform", "add", "multiply", "IPAD_MINI_MEDIUM_WIDTH", "forUIKit", "current", "next", "direction", "layouts", "defaultOffset", "leftSpacing", "OS", "screen", "width", "leftLabelOffset", "leftLabel", "titleLeftOffset", "title", "rightOffset", "multiplier", "progress", "interpolate", "inputRange", "outputRange", "extrapolate", "leftButtonStyle", "opacity", "leftLabelStyle", "transform", "translateX", "rightButtonStyle", "titleStyle", "backgroundStyle", "forFade", "forSlideLeft", "isRTL", "forSlideRight", "forSlideUp", "header", "translateY", "height", "forNoAnimation"], "sourceRoot": "../../../src", "sources": ["TransitionConfigs/HeaderStyleInterpolators.tsx"], "mappings": ";;AAAA,SAASA,QAAQ,EAAEC,QAAQ,QAAQ,cAAc;AAOjD,MAAM;EAAEC,GAAG;EAAEC;AAAS,CAAC,GAAGH,QAAQ;;AAElC;AACA;AACA,MAAMI,sBAAsB,GAAG,GAAG;;AAElC;AACA;AACA;AACA,OAAO,SAASC,QAAQA,CAAC;EACvBC,OAAO;EACPC,IAAI;EACJC,SAAS;EACTC;AAC6B,CAAC,EAAgC;EAC9D,MAAMC,aAAa,GAAG,GAAG;EACzB,MAAMC,WAAW,GACf,EAAE,IACDV,QAAQ,CAACW,EAAE,KAAK,KAAK,IAAIH,OAAO,CAACI,MAAM,CAACC,KAAK,IAAIV,sBAAsB,GACpE,CAAC,CAAC;EAAA,EACF,CAAC,CAAC;;EAER;EACA;EACA;EACA;EACA;EACA,MAAMW,eAAe,GAAGN,OAAO,CAACO,SAAS,GACrC,CAACP,OAAO,CAACI,MAAM,CAACC,KAAK,GAAGL,OAAO,CAACO,SAAS,CAACF,KAAK,IAAI,CAAC,GAAGH,WAAW,GAClED,aAAa;EACjB,MAAMO,eAAe,GAAGR,OAAO,CAACS,KAAK,GACjC,CAACT,OAAO,CAACI,MAAM,CAACC,KAAK,GAAGL,OAAO,CAACS,KAAK,CAACJ,KAAK,IAAI,CAAC,GAAGH,WAAW,GAC9DD,aAAa;;EAEjB;EACA;EACA,MAAMS,WAAW,GAAGV,OAAO,CAACI,MAAM,CAACC,KAAK,GAAG,CAAC;EAE5C,MAAMM,UAAU,GAAGZ,SAAS,KAAK,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC;EAE/C,MAAMa,QAAQ,GAAGnB,GAAG,CAClBI,OAAO,CAACe,QAAQ,CAACC,WAAW,CAAC;IAC3BC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IAClBC,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IACnBC,WAAW,EAAE;EACf,CAAC,CAAC,EACFlB,IAAI,GACAA,IAAI,CAACc,QAAQ,CAACC,WAAW,CAAC;IACxBC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IAClBC,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IACnBC,WAAW,EAAE;EACf,CAAC,CAAC,GACF,CACN,CAAC;EAED,OAAO;IACLC,eAAe,EAAE;MACfC,OAAO,EAAEN,QAAQ,CAACC,WAAW,CAAC;QAC5BC,UAAU,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG,CAAC;QACzBC,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;MACvB,CAAC;IACH,CAAC;IACDI,cAAc,EAAE;MACdC,SAAS,EAAE,CACT;QACEC,UAAU,EAAE3B,QAAQ,CAClBiB,UAAU,EACVC,QAAQ,CAACC,WAAW,CAAC;UACnBC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UACrBC,WAAW,EAAE,CAACT,eAAe,EAAE,CAAC,EAAE,CAACI,WAAW;QAChD,CAAC,CACH;MACF,CAAC;IAEL,CAAC;IACDY,gBAAgB,EAAE;MAChBJ,OAAO,EAAEN,QAAQ,CAACC,WAAW,CAAC;QAC5BC,UAAU,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG,CAAC;QACzBC,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;MACvB,CAAC;IACH,CAAC;IACDQ,UAAU,EAAE;MACVL,OAAO,EAAEN,QAAQ,CAACC,WAAW,CAAC;QAC5BC,UAAU,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC,EAAE,GAAG,CAAC;QAClCC,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;MAC/B,CAAC,CAAC;MACFK,SAAS,EAAE,CACT;QACEC,UAAU,EAAE3B,QAAQ,CAClBiB,UAAU,EACVC,QAAQ,CAACC,WAAW,CAAC;UACnBC,UAAU,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;UACvBC,WAAW,EAAE,CAACL,WAAW,EAAE,CAAC,EAAE,CAACF,eAAe;QAChD,CAAC,CACH;MACF,CAAC;IAEL,CAAC;IACDgB,eAAe,EAAE;MACfJ,SAAS,EAAE,CACT;QACEC,UAAU,EAAE3B,QAAQ,CAClBiB,UAAU,EACVC,QAAQ,CAACC,WAAW,CAAC;UACnBC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UACrBC,WAAW,EAAE,CAACf,OAAO,CAACI,MAAM,CAACC,KAAK,EAAE,CAAC,EAAE,CAACL,OAAO,CAACI,MAAM,CAACC,KAAK;QAC9D,CAAC,CACH;MACF,CAAC;IAEL;EACF,CAAC;AACH;;AAEA;AACA;AACA;AACA,OAAO,SAASoB,OAAOA,CAAC;EACtB5B,OAAO;EACPC;AAC6B,CAAC,EAAgC;EAC9D,MAAMc,QAAQ,GAAGnB,GAAG,CAClBI,OAAO,CAACe,QAAQ,CAACC,WAAW,CAAC;IAC3BC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IAClBC,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IACnBC,WAAW,EAAE;EACf,CAAC,CAAC,EACFlB,IAAI,GACAA,IAAI,CAACc,QAAQ,CAACC,WAAW,CAAC;IACxBC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IAClBC,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IACnBC,WAAW,EAAE;EACf,CAAC,CAAC,GACF,CACN,CAAC;EAED,MAAME,OAAO,GAAGN,QAAQ,CAACC,WAAW,CAAC;IACnCC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACrBC,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;EACvB,CAAC,CAAC;EAEF,OAAO;IACLE,eAAe,EAAE;MAAEC;IAAQ,CAAC;IAC5BI,gBAAgB,EAAE;MAAEJ;IAAQ,CAAC;IAC7BK,UAAU,EAAE;MAAEL;IAAQ,CAAC;IACvBM,eAAe,EAAE;MACfN,OAAO,EAAEN,QAAQ,CAACC,WAAW,CAAC;QAC5BC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;QAC1BC,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;MAC1B,CAAC;IACH;EACF,CAAC;AACH;;AAEA;AACA;AACA;AACA,OAAO,SAASW,YAAYA,CAAC;EAC3B7B,OAAO;EACPC,IAAI;EACJC,SAAS;EACTC,OAAO,EAAE;IAAEI;EAAO;AACW,CAAC,EAAgC;EAC9D,MAAMuB,KAAK,GAAG5B,SAAS,KAAK,KAAK;EACjC,MAAMa,QAAQ,GAAGnB,GAAG,CAClBI,OAAO,CAACe,QAAQ,CAACC,WAAW,CAAC;IAC3BC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IAClBC,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IACnBC,WAAW,EAAE;EACf,CAAC,CAAC,EACFlB,IAAI,GACAA,IAAI,CAACc,QAAQ,CAACC,WAAW,CAAC;IACxBC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IAClBC,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IACnBC,WAAW,EAAE;EACf,CAAC,CAAC,GACF,CACN,CAAC;EAED,MAAMK,UAAU,GAAGT,QAAQ,CAACC,WAAW,CAAC;IACtCC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACrBC,WAAW,EAAEY,KAAK,GACd,CAAC,CAACvB,MAAM,CAACC,KAAK,EAAE,CAAC,EAAED,MAAM,CAACC,KAAK,CAAC,GAChC,CAACD,MAAM,CAACC,KAAK,EAAE,CAAC,EAAE,CAACD,MAAM,CAACC,KAAK;EACrC,CAAC,CAAC;EAEF,MAAMe,SAAS,GAAG,CAAC;IAAEC;EAAW,CAAC,CAAC;EAElC,OAAO;IACLJ,eAAe,EAAE;MAAEG;IAAU,CAAC;IAC9BE,gBAAgB,EAAE;MAAEF;IAAU,CAAC;IAC/BG,UAAU,EAAE;MAAEH;IAAU,CAAC;IACzBI,eAAe,EAAE;MAAEJ;IAAU;EAC/B,CAAC;AACH;;AAEA;AACA;AACA;AACA,OAAO,SAASQ,aAAaA,CAAC;EAC5B/B,OAAO;EACPC,IAAI;EACJC,SAAS;EACTC,OAAO,EAAE;IAAEI;EAAO;AACW,CAAC,EAAgC;EAC9D,MAAMuB,KAAK,GAAG5B,SAAS,KAAK,KAAK;EACjC,MAAMa,QAAQ,GAAGnB,GAAG,CAClBI,OAAO,CAACe,QAAQ,CAACC,WAAW,CAAC;IAC3BC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IAClBC,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IACnBC,WAAW,EAAE;EACf,CAAC,CAAC,EACFlB,IAAI,GACAA,IAAI,CAACc,QAAQ,CAACC,WAAW,CAAC;IACxBC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IAClBC,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IACnBC,WAAW,EAAE;EACf,CAAC,CAAC,GACF,CACN,CAAC;EAED,MAAMK,UAAU,GAAGT,QAAQ,CAACC,WAAW,CAAC;IACtCC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACrBC,WAAW,EAAEY,KAAK,GACd,CAACvB,MAAM,CAACC,KAAK,EAAE,CAAC,EAAE,CAACD,MAAM,CAACC,KAAK,CAAC,GAChC,CAAC,CAACD,MAAM,CAACC,KAAK,EAAE,CAAC,EAAED,MAAM,CAACC,KAAK;EACrC,CAAC,CAAC;EAEF,MAAMe,SAAS,GAAG,CAAC;IAAEC;EAAW,CAAC,CAAC;EAElC,OAAO;IACLJ,eAAe,EAAE;MAAEG;IAAU,CAAC;IAC9BE,gBAAgB,EAAE;MAAEF;IAAU,CAAC;IAC/BG,UAAU,EAAE;MAAEH;IAAU,CAAC;IACzBI,eAAe,EAAE;MAAEJ;IAAU;EAC/B,CAAC;AACH;;AAEA;AACA;AACA;AACA,OAAO,SAASS,UAAUA,CAAC;EACzBhC,OAAO;EACPC,IAAI;EACJE,OAAO,EAAE;IAAE8B;EAAO;AACW,CAAC,EAAgC;EAC9D,MAAMlB,QAAQ,GAAGnB,GAAG,CAClBI,OAAO,CAACe,QAAQ,CAACC,WAAW,CAAC;IAC3BC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IAClBC,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IACnBC,WAAW,EAAE;EACf,CAAC,CAAC,EACFlB,IAAI,GACAA,IAAI,CAACc,QAAQ,CAACC,WAAW,CAAC;IACxBC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IAClBC,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IACnBC,WAAW,EAAE;EACf,CAAC,CAAC,GACF,CACN,CAAC;EAED,MAAMe,UAAU,GAAGnB,QAAQ,CAACC,WAAW,CAAC;IACtCC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACrBC,WAAW,EAAE,CAAC,CAACe,MAAM,CAACE,MAAM,EAAE,CAAC,EAAE,CAACF,MAAM,CAACE,MAAM;EACjD,CAAC,CAAC;EAEF,MAAMZ,SAAS,GAAG,CAAC;IAAEW;EAAW,CAAC,CAAC;EAElC,OAAO;IACLd,eAAe,EAAE;MAAEG;IAAU,CAAC;IAC9BE,gBAAgB,EAAE;MAAEF;IAAU,CAAC;IAC/BG,UAAU,EAAE;MAAEH;IAAU,CAAC;IACzBI,eAAe,EAAE;MAAEJ;IAAU;EAC/B,CAAC;AACH;AAEA,OAAO,SAASa,cAAcA,CAAA,EAAiC;EAC7D,OAAO,CAAC,CAAC;AACX", "ignoreList": []}