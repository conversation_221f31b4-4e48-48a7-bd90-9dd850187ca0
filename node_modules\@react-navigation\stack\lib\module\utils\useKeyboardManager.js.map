{"version": 3, "names": ["React", "Keyboard", "TextInput", "useKeyboardManager", "isEnabled", "previouslyFocusedTextInputRef", "useRef", "undefined", "startTimestampRef", "keyboardTimeoutRef", "clearKeyboardTimeout", "useCallback", "current", "clearTimeout", "onPageChangeStart", "input", "State", "currentlyFocusedInput", "blur", "Date", "now", "onPageChangeConfirm", "force", "dismiss", "onPageChangeCancel", "setTimeout", "focus", "useEffect"], "sourceRoot": "../../../src", "sources": ["utils/useKeyboardManager.tsx"], "mappings": ";;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAA4BC,QAAQ,EAAEC,SAAS,QAAQ,cAAc;AAErE,OAAO,SAASC,kBAAkBA,CAACC,SAAwB,EAAE;EAC3D;EACA;EACA,MAAMC,6BAA6B,GAAGL,KAAK,CAACM,MAAM,CAAeC,SAAS,CAAC;EAC3E,MAAMC,iBAAiB,GAAGR,KAAK,CAACM,MAAM,CAAS,CAAC,CAAC;EACjD,MAAMG,kBAAkB,GAAGT,KAAK,CAACM,MAAM,CAAiBC,SAAS,CAAC;EAElE,MAAMG,oBAAoB,GAAGV,KAAK,CAACW,WAAW,CAAC,MAAM;IACnD,IAAIF,kBAAkB,CAACG,OAAO,KAAKL,SAAS,EAAE;MAC5CM,YAAY,CAACJ,kBAAkB,CAACG,OAAO,CAAC;MACxCH,kBAAkB,CAACG,OAAO,GAAGL,SAAS;IACxC;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMO,iBAAiB,GAAGd,KAAK,CAACW,WAAW,CAAC,MAAM;IAChD,IAAI,CAACP,SAAS,CAAC,CAAC,EAAE;MAChB;IACF;IAEAM,oBAAoB,CAAC,CAAC;IAEtB,MAAMK,KAAK,GAAGb,SAAS,CAACc,KAAK,CAACC,qBAAqB,CAAC,CAAC;;IAErD;IACAF,KAAK,EAAEG,IAAI,CAAC,CAAC;;IAEb;IACAb,6BAA6B,CAACO,OAAO,GAAGG,KAAK;;IAE7C;IACAP,iBAAiB,CAACI,OAAO,GAAGO,IAAI,CAACC,GAAG,CAAC,CAAC;EACxC,CAAC,EAAE,CAACV,oBAAoB,EAAEN,SAAS,CAAC,CAAC;EAErC,MAAMiB,mBAAmB,GAAGrB,KAAK,CAACW,WAAW,CAC1CW,KAAc,IAAK;IAClB,IAAI,CAAClB,SAAS,CAAC,CAAC,EAAE;MAChB;IACF;IAEAM,oBAAoB,CAAC,CAAC;IAEtB,IAAIY,KAAK,EAAE;MACT;MACA;MACA;MACArB,QAAQ,CAACsB,OAAO,CAAC,CAAC;IACpB,CAAC,MAAM;MACL,MAAMR,KAAK,GAAGV,6BAA6B,CAACO,OAAO;;MAEnD;MACA;MACAG,KAAK,EAAEG,IAAI,CAAC,CAAC;IACf;;IAEA;IACAb,6BAA6B,CAACO,OAAO,GAAGL,SAAS;EACnD,CAAC,EACD,CAACG,oBAAoB,EAAEN,SAAS,CAClC,CAAC;EAED,MAAMoB,kBAAkB,GAAGxB,KAAK,CAACW,WAAW,CAAC,MAAM;IACjD,IAAI,CAACP,SAAS,CAAC,CAAC,EAAE;MAChB;IACF;IAEAM,oBAAoB,CAAC,CAAC;;IAEtB;IACA,MAAMK,KAAK,GAAGV,6BAA6B,CAACO,OAAO;IAEnD,IAAIG,KAAK,EAAE;MACT;;MAEA;MACA;MACA;MACA;MACA;MACA,IAAII,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGZ,iBAAiB,CAACI,OAAO,GAAG,GAAG,EAAE;QAChDH,kBAAkB,CAACG,OAAO,GAAGa,UAAU,CAAC,MAAM;UAC5CV,KAAK,EAAEW,KAAK,CAAC,CAAC;UACdrB,6BAA6B,CAACO,OAAO,GAAGL,SAAS;QACnD,CAAC,EAAE,GAAG,CAAC;MACT,CAAC,MAAM;QACLQ,KAAK,EAAEW,KAAK,CAAC,CAAC;QACdrB,6BAA6B,CAACO,OAAO,GAAGL,SAAS;MACnD;IACF;EACF,CAAC,EAAE,CAACG,oBAAoB,EAAEN,SAAS,CAAC,CAAC;EAErCJ,KAAK,CAAC2B,SAAS,CAAC,MAAM;IACpB,OAAO,MAAMjB,oBAAoB,CAAC,CAAC;EACrC,CAAC,EAAE,CAACA,oBAAoB,CAAC,CAAC;EAE1B,OAAO;IACLI,iBAAiB;IACjBO,mBAAmB;IACnBG;EACF,CAAC;AACH", "ignoreList": []}