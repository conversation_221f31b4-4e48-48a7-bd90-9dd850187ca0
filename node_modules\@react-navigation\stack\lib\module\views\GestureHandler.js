"use strict";

import * as React from 'react';
import { View } from 'react-native';
import { Fragment as _Fragment, jsx as _jsx } from "react/jsx-runtime";
const Dummy = ({
  children
}) => /*#__PURE__*/_jsx(_Fragment, {
  children: children
});
export const PanGestureHandler = Dummy;
export const GestureHandlerRootView = View;
export const GestureState = {
  UNDETERMINED: 0,
  FAILED: 1,
  BEGAN: 2,
  CANCELLED: 3,
  ACTIVE: 4,
  END: 5
};
//# sourceMappingURL=GestureHandler.js.map