import React from 'react';
import {
  TouchableOpacity,
  Text,
  StyleSheet,
  ActivityIndicator,
  View,
} from 'react-native';
import { Colors, Typography, Spacing } from '../../constants';

const Button = ({
  title,
  onPress,
  variant = 'primary',
  size = 'medium',
  disabled = false,
  loading = false,
  icon,
  iconPosition = 'left',
  fullWidth = false,
  style,
  textStyle,
  ...props
}) => {
  const getButtonStyle = () => {
    const baseStyle = [styles.button];
    
    // Size styles
    switch (size) {
      case 'small':
        baseStyle.push(styles.buttonSmall);
        break;
      case 'large':
        baseStyle.push(styles.buttonLarge);
        break;
      default:
        baseStyle.push(styles.buttonMedium);
    }
    
    // Variant styles
    switch (variant) {
      case 'secondary':
        baseStyle.push(styles.buttonSecondary);
        break;
      case 'outline':
        baseStyle.push(styles.buttonOutline);
        break;
      case 'ghost':
        baseStyle.push(styles.buttonGhost);
        break;
      case 'danger':
        baseStyle.push(styles.buttonDanger);
        break;
      default:
        baseStyle.push(styles.buttonPrimary);
    }
    
    // State styles
    if (disabled) {
      baseStyle.push(styles.buttonDisabled);
    }
    
    if (fullWidth) {
      baseStyle.push(styles.buttonFullWidth);
    }
    
    return baseStyle;
  };
  
  const getTextStyle = () => {
    const baseStyle = [styles.buttonText];
    
    // Size text styles
    switch (size) {
      case 'small':
        baseStyle.push(styles.buttonTextSmall);
        break;
      case 'large':
        baseStyle.push(styles.buttonTextLarge);
        break;
      default:
        baseStyle.push(styles.buttonTextMedium);
    }
    
    // Variant text styles
    switch (variant) {
      case 'secondary':
        baseStyle.push(styles.buttonTextSecondary);
        break;
      case 'outline':
        baseStyle.push(styles.buttonTextOutline);
        break;
      case 'ghost':
        baseStyle.push(styles.buttonTextGhost);
        break;
      case 'danger':
        baseStyle.push(styles.buttonTextDanger);
        break;
      default:
        baseStyle.push(styles.buttonTextPrimary);
    }
    
    if (disabled) {
      baseStyle.push(styles.buttonTextDisabled);
    }
    
    return baseStyle;
  };
  
  const renderContent = () => {
    if (loading) {
      return (
        <ActivityIndicator
          size="small"
          color={variant === 'primary' || variant === 'danger' ? Colors.neutral.white : Colors.primary.main}
        />
      );
    }
    
    const textElement = <Text style={[getTextStyle(), textStyle]}>{title}</Text>;
    
    if (!icon) {
      return textElement;
    }
    
    return (
      <View style={styles.buttonContent}>
        {iconPosition === 'left' && icon}
        {textElement}
        {iconPosition === 'right' && icon}
      </View>
    );
  };
  
  return (
    <TouchableOpacity
      style={[getButtonStyle(), style]}
      onPress={onPress}
      disabled={disabled || loading}
      activeOpacity={0.7}
      {...props}
    >
      {renderContent()}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  button: {
    borderRadius: Spacing.radius.lg,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
    ...Spacing.shadow.sm,
  },
  
  // Size styles
  buttonSmall: {
    paddingHorizontal: Spacing.component.buttonPaddingSmall.horizontal,
    paddingVertical: Spacing.component.buttonPaddingSmall.vertical,
    minHeight: 36,
  },
  buttonMedium: {
    paddingHorizontal: Spacing.component.buttonPadding.horizontal,
    paddingVertical: Spacing.component.buttonPadding.vertical,
    minHeight: 48,
  },
  buttonLarge: {
    paddingHorizontal: Spacing.component.buttonPaddingLarge.horizontal,
    paddingVertical: Spacing.component.buttonPaddingLarge.vertical,
    minHeight: 56,
  },
  
  // Variant styles
  buttonPrimary: {
    backgroundColor: Colors.primary.main,
  },
  buttonSecondary: {
    backgroundColor: Colors.secondary.main,
  },
  buttonOutline: {
    backgroundColor: 'transparent',
    borderWidth: 2,
    borderColor: Colors.primary.main,
  },
  buttonGhost: {
    backgroundColor: 'transparent',
  },
  buttonDanger: {
    backgroundColor: Colors.error.main,
  },
  
  // State styles
  buttonDisabled: {
    backgroundColor: Colors.neutral[200],
    shadowOpacity: 0,
    elevation: 0,
  },
  buttonFullWidth: {
    width: '100%',
  },
  
  // Text styles
  buttonText: {
    textAlign: 'center',
    includeFontPadding: false,
  },
  buttonTextSmall: {
    ...Typography.styles.buttonSmall,
  },
  buttonTextMedium: {
    ...Typography.styles.button,
  },
  buttonTextLarge: {
    ...Typography.styles.buttonLarge,
  },
  
  // Variant text styles
  buttonTextPrimary: {
    color: Colors.neutral.white,
  },
  buttonTextSecondary: {
    color: Colors.neutral.white,
  },
  buttonTextOutline: {
    color: Colors.primary.main,
  },
  buttonTextGhost: {
    color: Colors.primary.main,
  },
  buttonTextDanger: {
    color: Colors.neutral.white,
  },
  buttonTextDisabled: {
    color: Colors.neutral[400],
  },
  
  // Content layout
  buttonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.component.buttonGap,
  },
});

export default Button;
