import React from 'react';
import { View, StyleSheet, TouchableOpacity } from 'react-native';
import { Colors, Spacing } from '../../constants';

const Card = ({
  children,
  style,
  onPress,
  variant = 'default',
  padding = 'default',
  shadow = 'default',
  borderRadius = 'default',
  ...props
}) => {
  const getCardStyle = () => {
    const baseStyle = [styles.card];
    
    // Variant styles
    switch (variant) {
      case 'elevated':
        baseStyle.push(styles.cardElevated);
        break;
      case 'outlined':
        baseStyle.push(styles.cardOutlined);
        break;
      case 'flat':
        baseStyle.push(styles.cardFlat);
        break;
      default:
        baseStyle.push(styles.cardDefault);
    }
    
    // Padding styles
    switch (padding) {
      case 'none':
        baseStyle.push(styles.paddingNone);
        break;
      case 'small':
        baseStyle.push(styles.paddingSmall);
        break;
      case 'large':
        baseStyle.push(styles.paddingLarge);
        break;
      default:
        baseStyle.push(styles.paddingDefault);
    }
    
    // Shadow styles
    switch (shadow) {
      case 'none':
        baseStyle.push(styles.shadowNone);
        break;
      case 'small':
        baseStyle.push(styles.shadowSmall);
        break;
      case 'large':
        baseStyle.push(styles.shadowLarge);
        break;
      default:
        baseStyle.push(styles.shadowDefault);
    }
    
    // Border radius styles
    switch (borderRadius) {
      case 'none':
        baseStyle.push(styles.radiusNone);
        break;
      case 'small':
        baseStyle.push(styles.radiusSmall);
        break;
      case 'large':
        baseStyle.push(styles.radiusLarge);
        break;
      default:
        baseStyle.push(styles.radiusDefault);
    }
    
    return baseStyle;
  };
  
  if (onPress) {
    return (
      <TouchableOpacity
        style={[getCardStyle(), style]}
        onPress={onPress}
        activeOpacity={0.8}
        {...props}
      >
        {children}
      </TouchableOpacity>
    );
  }
  
  return (
    <View style={[getCardStyle(), style]} {...props}>
      {children}
    </View>
  );
};

const styles = StyleSheet.create({
  card: {
    backgroundColor: Colors.background.card,
  },
  
  // Variant styles
  cardDefault: {
    backgroundColor: Colors.background.card,
  },
  cardElevated: {
    backgroundColor: Colors.background.card,
  },
  cardOutlined: {
    backgroundColor: Colors.background.card,
    borderWidth: 1,
    borderColor: Colors.border.light,
  },
  cardFlat: {
    backgroundColor: 'transparent',
  },
  
  // Padding styles
  paddingNone: {
    padding: 0,
  },
  paddingSmall: {
    padding: Spacing.sm,
  },
  paddingDefault: {
    padding: Spacing.component.cardPadding,
  },
  paddingLarge: {
    padding: Spacing.lg,
  },
  
  // Shadow styles
  shadowNone: {
    shadowOpacity: 0,
    elevation: 0,
  },
  shadowSmall: {
    ...Spacing.shadow.sm,
  },
  shadowDefault: {
    ...Spacing.shadow.md,
  },
  shadowLarge: {
    ...Spacing.shadow.lg,
  },
  
  // Border radius styles
  radiusNone: {
    borderRadius: 0,
  },
  radiusSmall: {
    borderRadius: Spacing.radius.sm,
  },
  radiusDefault: {
    borderRadius: Spacing.radius.md,
  },
  radiusLarge: {
    borderRadius: Spacing.radius.lg,
  },
});

export default Card;
