import React, { useState } from 'react';
import {
  View,
  TextInput,
  Text,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';
import { Colors, Typography, Spacing } from '../../constants';

const Input = ({
  label,
  placeholder,
  value,
  onChangeText,
  error,
  helperText,
  leftIcon,
  rightIcon,
  secureTextEntry = false,
  multiline = false,
  numberOfLines = 1,
  keyboardType = 'default',
  autoCapitalize = 'sentences',
  autoCorrect = true,
  editable = true,
  maxLength,
  style,
  inputStyle,
  containerStyle,
  labelStyle,
  errorStyle,
  helperTextStyle,
  onFocus,
  onBlur,
  ...props
}) => {
  const [isFocused, setIsFocused] = useState(false);
  const [isSecureVisible, setIsSecureVisible] = useState(false);
  
  const handleFocus = (e) => {
    setIsFocused(true);
    onFocus?.(e);
  };
  
  const handleBlur = (e) => {
    setIsFocused(false);
    onBlur?.(e);
  };
  
  const toggleSecureVisibility = () => {
    setIsSecureVisible(!isSecureVisible);
  };
  
  const getInputContainerStyle = () => {
    const baseStyle = [styles.inputContainer];
    
    if (isFocused) {
      baseStyle.push(styles.inputContainerFocused);
    }
    
    if (error) {
      baseStyle.push(styles.inputContainerError);
    }
    
    if (!editable) {
      baseStyle.push(styles.inputContainerDisabled);
    }
    
    return baseStyle;
  };
  
  const getInputStyle = () => {
    const baseStyle = [styles.input];
    
    if (leftIcon) {
      baseStyle.push(styles.inputWithLeftIcon);
    }
    
    if (rightIcon || secureTextEntry) {
      baseStyle.push(styles.inputWithRightIcon);
    }
    
    if (multiline) {
      baseStyle.push(styles.inputMultiline);
    }
    
    return baseStyle;
  };
  
  return (
    <View style={[styles.container, containerStyle]}>
      {label && (
        <Text style={[styles.label, labelStyle]}>
          {label}
        </Text>
      )}
      
      <View style={[getInputContainerStyle(), style]}>
        {leftIcon && (
          <View style={styles.leftIconContainer}>
            {leftIcon}
          </View>
        )}
        
        <TextInput
          style={[getInputStyle(), inputStyle]}
          placeholder={placeholder}
          placeholderTextColor={Colors.text.tertiary}
          value={value}
          onChangeText={onChangeText}
          secureTextEntry={secureTextEntry && !isSecureVisible}
          multiline={multiline}
          numberOfLines={numberOfLines}
          keyboardType={keyboardType}
          autoCapitalize={autoCapitalize}
          autoCorrect={autoCorrect}
          editable={editable}
          maxLength={maxLength}
          onFocus={handleFocus}
          onBlur={handleBlur}
          {...props}
        />
        
        {(rightIcon || secureTextEntry) && (
          <View style={styles.rightIconContainer}>
            {secureTextEntry ? (
              <TouchableOpacity
                onPress={toggleSecureVisibility}
                style={styles.secureToggle}
              >
                <Text style={styles.secureToggleText}>
                  {isSecureVisible ? '👁️' : '👁️‍🗨️'}
                </Text>
              </TouchableOpacity>
            ) : (
              rightIcon
            )}
          </View>
        )}
      </View>
      
      {error && (
        <Text style={[styles.errorText, errorStyle]}>
          {error}
        </Text>
      )}
      
      {helperText && !error && (
        <Text style={[styles.helperText, helperTextStyle]}>
          {helperText}
        </Text>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: Spacing.md,
  },
  
  label: {
    ...Typography.styles.label,
    color: Colors.text.primary,
    marginBottom: Spacing.xs,
  },
  
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.background.secondary,
    borderWidth: 1,
    borderColor: Colors.border.light,
    borderRadius: Spacing.radius.md,
    paddingHorizontal: Spacing.md,
    minHeight: 48,
  },
  
  inputContainerFocused: {
    borderColor: Colors.primary.main,
    backgroundColor: Colors.background.primary,
  },
  
  inputContainerError: {
    borderColor: Colors.error.main,
    backgroundColor: Colors.error.bg,
  },
  
  inputContainerDisabled: {
    backgroundColor: Colors.neutral[100],
    borderColor: Colors.neutral[200],
  },
  
  input: {
    flex: 1,
    ...Typography.styles.body,
    color: Colors.text.primary,
    paddingVertical: Spacing.sm,
    includeFontPadding: false,
    textAlignVertical: 'center',
  },
  
  inputWithLeftIcon: {
    marginLeft: Spacing.xs,
  },
  
  inputWithRightIcon: {
    marginRight: Spacing.xs,
  },
  
  inputMultiline: {
    textAlignVertical: 'top',
    paddingTop: Spacing.sm,
    paddingBottom: Spacing.sm,
  },
  
  leftIconContainer: {
    marginRight: Spacing.xs,
    alignItems: 'center',
    justifyContent: 'center',
  },
  
  rightIconContainer: {
    marginLeft: Spacing.xs,
    alignItems: 'center',
    justifyContent: 'center',
  },
  
  secureToggle: {
    padding: Spacing.xs,
  },
  
  secureToggleText: {
    fontSize: 16,
  },
  
  errorText: {
    ...Typography.styles.caption,
    color: Colors.error.main,
    marginTop: Spacing.xs,
  },
  
  helperText: {
    ...Typography.styles.caption,
    color: Colors.text.tertiary,
    marginTop: Spacing.xs,
  },
});

export default Input;
