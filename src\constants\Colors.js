// Color palette optimized for Pakistani food delivery app
// Warm and delicious colors with cultural relevance

export const Colors = {
  // Primary Colors - Spicy and appetizing
  primary: {
    main: '#E53E3E', // Spicy red - main brand color
    light: '#FC8181', // Light red for highlights
    dark: '#C53030', // Dark red for pressed states
    50: '#FEF5F5',
    100: '#FED7D7',
    200: '#FEB2B2',
    300: '#FC8181',
    400: '#F56565',
    500: '#E53E3E', // Main
    600: '#C53030',
    700: '#9B2C2C',
    800: '#822727',
    900: '#63171B',
  },

  // Secondary Colors - Fresh and natural
  secondary: {
    main: '#38A169', // Fresh green
    light: '#68D391',
    dark: '#2F855A',
    50: '#F0FFF4',
    100: '#C6F6D5',
    200: '#9AE6B4',
    300: '#68D391',
    400: '#48BB78',
    500: '#38A169', // Main
    600: '#2F855A',
    700: '#276749',
    800: '#22543D',
    900: '#1C4532',
  },

  // Accent Colors - Golden and premium
  accent: {
    main: '#D69E2E', // Golden yellow
    light: '#F6E05E',
    dark: '#B7791F',
    50: '#FFFBEB',
    100: '#FEF5E7',
    200: '#FED7AA',
    300: '#FDBA74',
    400: '#F59E0B',
    500: '#D69E2E', // Main
    600: '#B7791F',
    700: '#92400E',
    800: '#78350F',
    900: '#451A03',
  },

  // Neutral Colors - Clean and modern
  neutral: {
    white: '#FFFFFF',
    50: '#FAFAFA',
    100: '#F5F5F5',
    200: '#E5E5E5',
    300: '#D4D4D4',
    400: '#A3A3A3',
    500: '#737373',
    600: '#525252',
    700: '#404040',
    800: '#262626',
    900: '#171717',
    black: '#000000',
  },

  // Semantic Colors
  success: {
    main: '#38A169',
    light: '#68D391',
    dark: '#2F855A',
    bg: '#F0FFF4',
  },

  warning: {
    main: '#D69E2E',
    light: '#F6E05E',
    dark: '#B7791F',
    bg: '#FFFBEB',
  },

  error: {
    main: '#E53E3E',
    light: '#FC8181',
    dark: '#C53030',
    bg: '#FEF5F5',
  },

  info: {
    main: '#3182CE',
    light: '#63B3ED',
    dark: '#2C5282',
    bg: '#EBF8FF',
  },

  // Background Colors
  background: {
    primary: '#FFFFFF',
    secondary: '#FAFAFA',
    tertiary: '#F5F5F5',
    card: '#FFFFFF',
    overlay: 'rgba(0, 0, 0, 0.5)',
  },

  // Text Colors
  text: {
    primary: '#171717',
    secondary: '#525252',
    tertiary: '#737373',
    disabled: '#A3A3A3',
    inverse: '#FFFFFF',
    link: '#3182CE',
  },

  // Border Colors
  border: {
    light: '#E5E5E5',
    medium: '#D4D4D4',
    dark: '#A3A3A3',
  },

  // Dark Theme Colors
  dark: {
    background: {
      primary: '#171717',
      secondary: '#262626',
      tertiary: '#404040',
      card: '#262626',
      overlay: 'rgba(255, 255, 255, 0.1)',
    },
    text: {
      primary: '#FAFAFA',
      secondary: '#D4D4D4',
      tertiary: '#A3A3A3',
      disabled: '#737373',
      inverse: '#171717',
      link: '#63B3ED',
    },
    border: {
      light: '#404040',
      medium: '#525252',
      dark: '#737373',
    },
  },
};

// Theme configuration
export const lightTheme = {
  colors: {
    primary: Colors.primary.main,
    secondary: Colors.secondary.main,
    accent: Colors.accent.main,
    background: Colors.background.primary,
    surface: Colors.background.card,
    text: Colors.text.primary,
    textSecondary: Colors.text.secondary,
    border: Colors.border.light,
    success: Colors.success.main,
    warning: Colors.warning.main,
    error: Colors.error.main,
    info: Colors.info.main,
  },
};

export const darkTheme = {
  colors: {
    primary: Colors.primary.light,
    secondary: Colors.secondary.light,
    accent: Colors.accent.light,
    background: Colors.dark.background.primary,
    surface: Colors.dark.background.card,
    text: Colors.dark.text.primary,
    textSecondary: Colors.dark.text.secondary,
    border: Colors.dark.border.light,
    success: Colors.success.light,
    warning: Colors.warning.light,
    error: Colors.error.light,
    info: Colors.info.light,
  },
};
