// Spacing system for consistent layout
// Based on 8px grid system for better alignment

export const Spacing = {
  // Base spacing unit (8px)
  unit: 8,

  // Spacing scale
  xs: 4,    // 0.5 * unit
  sm: 8,    // 1 * unit
  md: 16,   // 2 * unit
  lg: 24,   // 3 * unit
  xl: 32,   // 4 * unit
  '2xl': 40, // 5 * unit
  '3xl': 48, // 6 * unit
  '4xl': 64, // 8 * unit
  '5xl': 80, // 10 * unit

  // Component-specific spacing
  component: {
    // Padding
    buttonPadding: {
      horizontal: 24,
      vertical: 12,
    },
    buttonPaddingLarge: {
      horizontal: 32,
      vertical: 16,
    },
    buttonPaddingSmall: {
      horizontal: 16,
      vertical: 8,
    },
    cardPadding: 16,
    screenPadding: 20,
    sectionPadding: 24,

    // Margins
    cardMargin: 8,
    sectionMargin: 16,
    itemMargin: 12,

    // Gaps
    listGap: 12,
    gridGap: 16,
    buttonGap: 8,
  },

  // Layout spacing
  layout: {
    headerHeight: 56,
    tabBarHeight: 60,
    bottomSafeArea: 34, // iPhone X bottom safe area
    statusBarHeight: 44, // iPhone X status bar
    
    // Container spacing
    containerPadding: 20,
    sectionSpacing: 32,
    
    // Navigation
    navPadding: 16,
    navItemSpacing: 12,
  },

  // Border radius
  radius: {
    none: 0,
    sm: 4,
    md: 8,
    lg: 12,
    xl: 16,
    '2xl': 20,
    '3xl': 24,
    full: 9999,
  },

  // Shadows and elevation
  shadow: {
    sm: {
      shadowColor: '#000',
      shadowOffset: {
        width: 0,
        height: 1,
      },
      shadowOpacity: 0.05,
      shadowRadius: 2,
      elevation: 1,
    },
    md: {
      shadowColor: '#000',
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 3,
    },
    lg: {
      shadowColor: '#000',
      shadowOffset: {
        width: 0,
        height: 4,
      },
      shadowOpacity: 0.15,
      shadowRadius: 8,
      elevation: 5,
    },
    xl: {
      shadowColor: '#000',
      shadowOffset: {
        width: 0,
        height: 8,
      },
      shadowOpacity: 0.2,
      shadowRadius: 16,
      elevation: 8,
    },
  },
};

// Helper functions
export const getSpacing = (size) => {
  return Spacing[size] || Spacing.md;
};

export const getRadius = (size) => {
  return Spacing.radius[size] || Spacing.radius.md;
};

export const getShadow = (size) => {
  return Spacing.shadow[size] || Spacing.shadow.md;
};
