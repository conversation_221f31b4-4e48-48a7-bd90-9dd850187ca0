// Typography system for Pakistani food delivery app
// Clean, readable fonts with proper hierarchy

export const Typography = {
  // Font Families
  fontFamily: {
    primary: 'Inter', // Clean and modern
    secondary: 'Poppins', // Friendly and rounded
    mono: 'Roboto Mono', // For numbers and codes
  },

  // Font Weights
  fontWeight: {
    light: '300',
    regular: '400',
    medium: '500',
    semiBold: '600',
    bold: '700',
    extraBold: '800',
  },

  // Font Sizes (using rem-like scale for consistency)
  fontSize: {
    xs: 12,
    sm: 14,
    base: 16,
    lg: 18,
    xl: 20,
    '2xl': 24,
    '3xl': 30,
    '4xl': 36,
    '5xl': 48,
    '6xl': 60,
  },

  // Line Heights
  lineHeight: {
    tight: 1.2,
    normal: 1.4,
    relaxed: 1.6,
    loose: 1.8,
  },

  // Letter Spacing
  letterSpacing: {
    tight: -0.5,
    normal: 0,
    wide: 0.5,
    wider: 1,
  },

  // Text Styles - Pre-defined combinations
  styles: {
    // Headers
    h1: {
      fontFamily: 'Poppins',
      fontSize: 36,
      fontWeight: '700',
      lineHeight: 1.2,
      letterSpacing: -0.5,
    },
    h2: {
      fontFamily: 'Poppins',
      fontSize: 30,
      fontWeight: '600',
      lineHeight: 1.2,
      letterSpacing: -0.5,
    },
    h3: {
      fontFamily: 'Poppins',
      fontSize: 24,
      fontWeight: '600',
      lineHeight: 1.3,
      letterSpacing: 0,
    },
    h4: {
      fontFamily: 'Poppins',
      fontSize: 20,
      fontWeight: '600',
      lineHeight: 1.3,
      letterSpacing: 0,
    },
    h5: {
      fontFamily: 'Poppins',
      fontSize: 18,
      fontWeight: '500',
      lineHeight: 1.4,
      letterSpacing: 0,
    },
    h6: {
      fontFamily: 'Poppins',
      fontSize: 16,
      fontWeight: '500',
      lineHeight: 1.4,
      letterSpacing: 0,
    },

    // Body Text
    bodyLarge: {
      fontFamily: 'Inter',
      fontSize: 18,
      fontWeight: '400',
      lineHeight: 1.6,
      letterSpacing: 0,
    },
    body: {
      fontFamily: 'Inter',
      fontSize: 16,
      fontWeight: '400',
      lineHeight: 1.5,
      letterSpacing: 0,
    },
    bodySmall: {
      fontFamily: 'Inter',
      fontSize: 14,
      fontWeight: '400',
      lineHeight: 1.4,
      letterSpacing: 0,
    },

    // Labels and UI Text
    label: {
      fontFamily: 'Inter',
      fontSize: 14,
      fontWeight: '500',
      lineHeight: 1.4,
      letterSpacing: 0,
    },
    labelSmall: {
      fontFamily: 'Inter',
      fontSize: 12,
      fontWeight: '500',
      lineHeight: 1.3,
      letterSpacing: 0.5,
    },

    // Buttons
    buttonLarge: {
      fontFamily: 'Poppins',
      fontSize: 18,
      fontWeight: '600',
      lineHeight: 1.2,
      letterSpacing: 0,
    },
    button: {
      fontFamily: 'Poppins',
      fontSize: 16,
      fontWeight: '600',
      lineHeight: 1.2,
      letterSpacing: 0,
    },
    buttonSmall: {
      fontFamily: 'Poppins',
      fontSize: 14,
      fontWeight: '500',
      lineHeight: 1.2,
      letterSpacing: 0,
    },

    // Special Text
    caption: {
      fontFamily: 'Inter',
      fontSize: 12,
      fontWeight: '400',
      lineHeight: 1.3,
      letterSpacing: 0,
    },
    overline: {
      fontFamily: 'Inter',
      fontSize: 12,
      fontWeight: '500',
      lineHeight: 1.3,
      letterSpacing: 1,
      textTransform: 'uppercase',
    },

    // Price and Numbers
    price: {
      fontFamily: 'Poppins',
      fontSize: 18,
      fontWeight: '700',
      lineHeight: 1.2,
      letterSpacing: 0,
    },
    priceSmall: {
      fontFamily: 'Poppins',
      fontSize: 14,
      fontWeight: '600',
      lineHeight: 1.2,
      letterSpacing: 0,
    },

    // Navigation
    tabLabel: {
      fontFamily: 'Inter',
      fontSize: 12,
      fontWeight: '500',
      lineHeight: 1.2,
      letterSpacing: 0,
    },
    navTitle: {
      fontFamily: 'Poppins',
      fontSize: 18,
      fontWeight: '600',
      lineHeight: 1.2,
      letterSpacing: 0,
    },
  },

  // Urdu/Arabic Text Support
  urdu: {
    fontFamily: 'Noto Sans Urdu', // Fallback to system fonts
    styles: {
      h1: {
        fontSize: 32,
        fontWeight: '700',
        lineHeight: 1.4,
        textAlign: 'right',
      },
      h2: {
        fontSize: 26,
        fontWeight: '600',
        lineHeight: 1.4,
        textAlign: 'right',
      },
      body: {
        fontSize: 16,
        fontWeight: '400',
        lineHeight: 1.6,
        textAlign: 'right',
      },
      button: {
        fontSize: 16,
        fontWeight: '600',
        lineHeight: 1.3,
        textAlign: 'center',
      },
    },
  },
};

// Helper function to get text style
export const getTextStyle = (styleName, theme = 'light') => {
  const baseStyle = Typography.styles[styleName] || Typography.styles.body;
  
  return {
    ...baseStyle,
    includeFontPadding: false, // Android optimization
    textAlignVertical: 'center', // Android optimization
  };
};

// Helper function for responsive font sizes
export const getResponsiveFontSize = (baseSize, screenWidth) => {
  const scale = screenWidth / 375; // Base on iPhone X width
  const minScale = 0.85;
  const maxScale = 1.15;
  
  const finalScale = Math.max(minScale, Math.min(maxScale, scale));
  return Math.round(baseSize * finalScale);
};
