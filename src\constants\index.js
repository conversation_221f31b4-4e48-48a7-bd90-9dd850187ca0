// Export all constants for easy importing
export { Colors, lightTheme, darkTheme } from './Colors';
export { Typography, getTextStyle, getResponsiveFontSize } from './Typography';
export { Spacing, getSpacing, getRadius, getShadow } from './Spacing';

// App-specific constants
export const AppConfig = {
  // App Information
  name: 'FoodieExpress',
  version: '1.0.0',
  
  // API Configuration
  apiUrl: 'https://api.foodieexpress.pk',
  imageBaseUrl: 'https://images.foodieexpress.pk',
  
  // Feature Flags
  features: {
    otpVerification: false, // Can be enabled later
    emailVerification: false, // Can be enabled later
    guestMode: true,
    darkMode: true,
    urduLanguage: true,
    pushNotifications: true,
  },
  
  // App Limits
  limits: {
    maxCartItems: 50,
    maxAddresses: 5,
    imageUploadSize: 5 * 1024 * 1024, // 5MB
    searchHistoryLimit: 10,
  },
  
  // Default Values
  defaults: {
    currency: 'PKR',
    language: 'en', // 'en' or 'ur'
    deliveryRadius: 10, // km
    minOrderAmount: 299, // PKR
    deliveryFee: 49, // PKR
    taxRate: 0.16, // 16% GST
  },
  
  // Payment Methods
  paymentMethods: [
    {
      id: 'cash',
      name: 'Cash on Delivery',
      nameUrdu: 'کیش آن ڈیلیوری',
      icon: 'cash',
      enabled: true,
    },
    {
      id: 'jazzcash',
      name: 'JazzCash',
      nameUrdu: 'جاز کیش',
      icon: 'mobile-payment',
      enabled: true,
    },
    {
      id: 'easypaisa',
      name: 'Easypaisa',
      nameUrdu: 'ایزی پیسہ',
      icon: 'mobile-payment',
      enabled: true,
    },
    {
      id: 'card',
      name: 'Credit/Debit Card',
      nameUrdu: 'کریڈٹ/ڈیبٹ کارڈ',
      icon: 'credit-card',
      enabled: false, // Can be enabled later
    },
  ],
  
  // Food Categories
  foodCategories: [
    {
      id: 'fast-food',
      name: 'Fast Food',
      nameUrdu: 'فاسٹ فوڈ',
      icon: '🍔',
      color: '#E53E3E',
    },
    {
      id: 'desi',
      name: 'Desi',
      nameUrdu: 'دیسی',
      icon: '🍛',
      color: '#D69E2E',
    },
    {
      id: 'biryani',
      name: 'Biryani',
      nameUrdu: 'بریانی',
      icon: '🍚',
      color: '#38A169',
    },
    {
      id: 'bbq',
      name: 'BBQ',
      nameUrdu: 'بی بی کیو',
      icon: '🍖',
      color: '#C53030',
    },
    {
      id: 'pizza',
      name: 'Pizza',
      nameUrdu: 'پیزا',
      icon: '🍕',
      color: '#E53E3E',
    },
    {
      id: 'chinese',
      name: 'Chinese',
      nameUrdu: 'چائنیز',
      icon: '🥡',
      color: '#B7791F',
    },
    {
      id: 'desserts',
      name: 'Desserts',
      nameUrdu: 'میٹھائی',
      icon: '🍰',
      color: '#9B2C2C',
    },
    {
      id: 'drinks',
      name: 'Drinks',
      nameUrdu: 'مشروبات',
      icon: '🥤',
      color: '#3182CE',
    },
  ],
  
  // Cities (can be expanded)
  cities: [
    {
      id: 'karachi',
      name: 'Karachi',
      nameUrdu: 'کراچی',
      coordinates: { lat: 24.8607, lng: 67.0011 },
    },
    {
      id: 'lahore',
      name: 'Lahore',
      nameUrdu: 'لاہور',
      coordinates: { lat: 31.5204, lng: 74.3587 },
    },
    {
      id: 'islamabad',
      name: 'Islamabad',
      nameUrdu: 'اسلام آباد',
      coordinates: { lat: 33.6844, lng: 73.0479 },
    },
    {
      id: 'rawalpindi',
      name: 'Rawalpindi',
      nameUrdu: 'راولپنڈی',
      coordinates: { lat: 33.5651, lng: 73.0169 },
    },
    {
      id: 'faisalabad',
      name: 'Faisalabad',
      nameUrdu: 'فیصل آباد',
      coordinates: { lat: 31.4504, lng: 73.1350 },
    },
  ],
  
  // Order Status
  orderStatus: [
    {
      id: 'confirmed',
      name: 'Order Confirmed',
      nameUrdu: 'آرڈر کنفرم',
      icon: 'check-circle',
      color: '#38A169',
    },
    {
      id: 'preparing',
      name: 'Preparing',
      nameUrdu: 'تیار ہو رہا ہے',
      icon: 'chef-hat',
      color: '#D69E2E',
    },
    {
      id: 'on-the-way',
      name: 'On the Way',
      nameUrdu: 'راستے میں',
      icon: 'truck',
      color: '#3182CE',
    },
    {
      id: 'delivered',
      name: 'Delivered',
      nameUrdu: 'ڈیلیور ہو گیا',
      icon: 'check-circle-2',
      color: '#38A169',
    },
    {
      id: 'cancelled',
      name: 'Cancelled',
      nameUrdu: 'منسوخ',
      icon: 'x-circle',
      color: '#E53E3E',
    },
  ],
};

// Screen dimensions helper
export const ScreenDimensions = {
  // Common breakpoints
  small: 320,
  medium: 375,
  large: 414,
  tablet: 768,
  
  // Helper function to get responsive value
  getResponsiveValue: (screenWidth, small, medium, large) => {
    if (screenWidth < 375) return small;
    if (screenWidth < 414) return medium;
    return large;
  },
};
