import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { View, Text, StyleSheet } from 'react-native';

// Import screens (will be created later)
import SplashScreen from '../screens/SplashScreen';
import OnboardingScreen from '../screens/OnboardingScreen';
import LoginScreen from '../screens/auth/LoginScreen';
import SignupScreen from '../screens/auth/SignupScreen';
import HomeScreen from '../screens/HomeScreen';
import RestaurantScreen from '../screens/RestaurantScreen';
import CartScreen from '../screens/CartScreen';
import CheckoutScreen from '../screens/CheckoutScreen';
import OrderTrackingScreen from '../screens/OrderTrackingScreen';
import ProfileScreen from '../screens/ProfileScreen';
import SearchScreen from '../screens/SearchScreen';

import { Colors, Typography, Spacing } from '../constants';

const Stack = createNativeStackNavigator();
const Tab = createBottomTabNavigator();

// Tab Bar Icon Component
const TabBarIcon = ({ name, focused, color, size = 24 }) => {
  const icons = {
    Home: focused ? '🏠' : '🏡',
    Search: focused ? '🔍' : '🔎',
    Cart: focused ? '🛒' : '🛍️',
    Profile: focused ? '👤' : '👥',
  };
  
  return (
    <View style={styles.tabIconContainer}>
      <Text style={[styles.tabIcon, { color, fontSize: size }]}>
        {icons[name] || '📱'}
      </Text>
    </View>
  );
};

// Main Tab Navigator
const MainTabNavigator = () => {
  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => (
          <TabBarIcon
            name={route.name}
            focused={focused}
            color={color}
            size={size}
          />
        ),
        tabBarActiveTintColor: Colors.primary.main,
        tabBarInactiveTintColor: Colors.text.tertiary,
        tabBarStyle: styles.tabBar,
        tabBarLabelStyle: styles.tabBarLabel,
        headerShown: false,
        tabBarHideOnKeyboard: true,
      })}
    >
      <Tab.Screen
        name="Home"
        component={HomeScreen}
        options={{
          tabBarLabel: 'Home',
        }}
      />
      <Tab.Screen
        name="Search"
        component={SearchScreen}
        options={{
          tabBarLabel: 'Search',
        }}
      />
      <Tab.Screen
        name="Cart"
        component={CartScreen}
        options={{
          tabBarLabel: 'Cart',
          tabBarBadge: 3, // This will be dynamic based on cart items
        }}
      />
      <Tab.Screen
        name="Profile"
        component={ProfileScreen}
        options={{
          tabBarLabel: 'Profile',
        }}
      />
    </Tab.Navigator>
  );
};

// Auth Stack Navigator
const AuthStackNavigator = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
        gestureEnabled: true,
        animation: 'slide_from_right',
      }}
    >
      <Stack.Screen name="Login" component={LoginScreen} />
      <Stack.Screen name="Signup" component={SignupScreen} />
    </Stack.Navigator>
  );
};

// Main App Navigator
const AppNavigator = () => {
  // This would typically be managed by a context or state management
  const isFirstLaunch = false; // Set to true for first launch
  const isAuthenticated = true; // This would come from auth context
  
  return (
    <NavigationContainer>
      <Stack.Navigator
        screenOptions={{
          headerShown: false,
          gestureEnabled: true,
          animation: 'slide_from_right',
        }}
      >
        {/* Splash and Onboarding */}
        <Stack.Screen name="Splash" component={SplashScreen} />
        {isFirstLaunch && (
          <Stack.Screen name="Onboarding" component={OnboardingScreen} />
        )}
        
        {/* Auth Flow */}
        {!isAuthenticated && (
          <Stack.Screen
            name="Auth"
            component={AuthStackNavigator}
            options={{ gestureEnabled: false }}
          />
        )}
        
        {/* Main App Flow */}
        <Stack.Screen
          name="MainTabs"
          component={MainTabNavigator}
          options={{ gestureEnabled: false }}
        />
        
        {/* Modal Screens */}
        <Stack.Screen
          name="Restaurant"
          component={RestaurantScreen}
          options={{
            presentation: 'modal',
            headerShown: true,
            headerTitle: '',
            headerStyle: {
              backgroundColor: Colors.background.primary,
            },
            headerTintColor: Colors.text.primary,
          }}
        />
        
        <Stack.Screen
          name="Checkout"
          component={CheckoutScreen}
          options={{
            headerShown: true,
            headerTitle: 'Checkout',
            headerStyle: {
              backgroundColor: Colors.background.primary,
            },
            headerTitleStyle: {
              ...Typography.styles.navTitle,
              color: Colors.text.primary,
            },
            headerTintColor: Colors.text.primary,
          }}
        />
        
        <Stack.Screen
          name="OrderTracking"
          component={OrderTrackingScreen}
          options={{
            headerShown: true,
            headerTitle: 'Track Order',
            headerStyle: {
              backgroundColor: Colors.background.primary,
            },
            headerTitleStyle: {
              ...Typography.styles.navTitle,
              color: Colors.text.primary,
            },
            headerTintColor: Colors.text.primary,
            gestureEnabled: false, // Prevent going back during order tracking
          }}
        />
      </Stack.Navigator>
    </NavigationContainer>
  );
};

const styles = StyleSheet.create({
  tabBar: {
    backgroundColor: Colors.background.primary,
    borderTopWidth: 1,
    borderTopColor: Colors.border.light,
    height: Spacing.layout.tabBarHeight,
    paddingBottom: Spacing.xs,
    paddingTop: Spacing.xs,
    ...Spacing.shadow.sm,
  },
  
  tabBarLabel: {
    ...Typography.styles.tabLabel,
    marginTop: 2,
  },
  
  tabIconContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    width: 24,
    height: 24,
  },
  
  tabIcon: {
    textAlign: 'center',
  },
});

export default AppNavigator;
