import { useState } from 'react';
import {
    ScrollView,
    StatusBar,
    StyleSheet,
    Text,
    TouchableOpacity,
    View
} from 'react-native';
import Button from '../components/common/Button';
import Card from '../components/common/Card';
import { AppConfig, Colors, Spacing, Typography } from '../constants';

// Mock cart data
const mockCartItems = [
  {
    id: '1',
    name: 'Chicken Biryani',
    nameUrdu: 'چکن بریانی',
    price: 450,
    quantity: 2,
    restaurant: 'Karachi Biryani House',
    image: '🍛',
  },
  {
    id: '2',
    name: 'Margherita Pizza',
    nameUrdu: 'مارگریٹا پیزا',
    price: 850,
    quantity: 1,
    restaurant: 'Pizza Palace',
    image: '🍕',
  },
];

// Mock promo codes
const promoCodes = {
  'SAVE20': { discount: 20, type: 'percentage', minOrder: 500 },
  'FLAT50': { discount: 50, type: 'fixed', minOrder: 300 },
  'WELCOME10': { discount: 10, type: 'percentage', minOrder: 200 },
};

// Mock delivery addresses
const mockAddresses = [
  {
    id: '1',
    type: 'Home',
    address: 'House 123, Block A, Gulshan-e-Iqbal, Karachi',
    isDefault: true,
  },
  {
    id: '2',
    type: 'Office',
    address: 'Office 456, Floor 5, Business Center, Clifton, Karachi',
    isDefault: false,
  },
];

const CartScreen = ({ navigation, route }) => {
  const { cartItems: routeCartItems } = route.params || {};
  const [cartItems, setCartItems] = useState(routeCartItems || mockCartItems);
  const [isUrdu, setIsUrdu] = useState(false);
  const [promoCode, setPromoCode] = useState('');
  const [appliedPromo, setAppliedPromo] = useState(null);
  const [showPromoInput, setShowPromoInput] = useState(false);
  const [selectedAddress, setSelectedAddress] = useState(mockAddresses[0]);
  const [showAddressModal, setShowAddressModal] = useState(false);
  const [showItemModal, setShowItemModal] = useState(false);
  const [selectedItem, setSelectedItem] = useState(null);

  const subtotal = cartItems.reduce((sum, item) => sum + (item.price * item.quantity), 0);
  const deliveryFee = AppConfig.defaults.deliveryFee;
  const tax = Math.round(subtotal * AppConfig.defaults.taxRate);

  // Calculate discount
  let discount = 0;
  if (appliedPromo) {
    if (appliedPromo.type === 'percentage') {
      discount = Math.round((subtotal * appliedPromo.discount) / 100);
    } else {
      discount = appliedPromo.discount;
    }
  }

  const total = subtotal + deliveryFee + tax - discount;
  
  const updateQuantity = (itemId, newQuantity) => {
    if (newQuantity === 0) {
      Alert.alert(
        'Remove Item',
        'Are you sure you want to remove this item from cart?',
        [
          { text: 'Cancel', style: 'cancel' },
          {
            text: 'Remove',
            style: 'destructive',
            onPress: () => setCartItems(items => items.filter(item => item.id !== itemId))
          },
        ]
      );
    } else {
      setCartItems(items =>
        items.map(item =>
          item.id === itemId ? { ...item, quantity: newQuantity } : item
        )
      );
    }
  };

  const applyPromoCode = () => {
    const promo = promoCodes[promoCode.toUpperCase()];
    if (!promo) {
      Alert.alert('Invalid Code', 'Please enter a valid promo code');
      return;
    }

    if (subtotal < promo.minOrder) {
      Alert.alert(
        'Minimum Order Required',
        `Minimum order of PKR ${promo.minOrder} required for this promo code`
      );
      return;
    }

    setAppliedPromo(promo);
    setShowPromoInput(false);
    setPromoCode('');
    Alert.alert('Success', 'Promo code applied successfully!');
  };

  const removePromoCode = () => {
    setAppliedPromo(null);
    Alert.alert('Removed', 'Promo code removed');
  };

  const handleItemEdit = (item) => {
    setSelectedItem(item);
    setShowItemModal(true);
  };

  const removeItem = (itemId) => {
    Alert.alert(
      'Remove Item',
      'Are you sure you want to remove this item from cart?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Remove',
          style: 'destructive',
          onPress: () => setCartItems(items => items.filter(item => item.id !== itemId))
        },
      ]
    );
  };

  const handleCheckout = () => {
    navigation.navigate('Checkout', {
      cartItems,
      total,
      subtotal,
      deliveryFee,
      tax,
      discount,
      appliedPromo,
      selectedAddress
    });
  };
  
  const renderCartItem = (item) => (
    <Card key={item.id} style={styles.cartItem}>
      <View style={styles.itemContent}>
        <View style={styles.itemHeader}>
          <Text style={styles.itemEmoji}>{item.image}</Text>
          <View style={styles.itemInfo}>
            <Text style={styles.itemName}>
              {isUrdu ? item.nameUrdu : item.name}
            </Text>
            <Text style={styles.itemRestaurant}>{item.restaurant}</Text>
            {item.customizations && Object.keys(item.customizations).length > 0 && (
              <Text style={styles.itemCustomizations}>
                {Object.entries(item.customizations).map(([key, value]) =>
                  `${key}: ${value}`
                ).join(', ')}
              </Text>
            )}
            <Text style={styles.itemPrice}>PKR {item.price} × {item.quantity}</Text>
            <Text style={styles.itemTotal}>PKR {item.totalPrice || (item.price * item.quantity)}</Text>
          </View>

          <View style={styles.itemActions}>
            <TouchableOpacity
              style={styles.editButton}
              onPress={() => handleItemEdit(item)}
            >
              <Text style={styles.editButtonText}>✏️</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.removeButton}
              onPress={() => removeItem(item.id)}
            >
              <Text style={styles.removeButtonText}>🗑️</Text>
            </TouchableOpacity>
          </View>
        </View>

        <View style={styles.quantityControls}>
          <TouchableOpacity
            style={styles.quantityButton}
            onPress={() => updateQuantity(item.id, item.quantity - 1)}
          >
            <Text style={styles.quantityButtonText}>−</Text>
          </TouchableOpacity>

          <Text style={styles.quantityText}>{item.quantity}</Text>

          <TouchableOpacity
            style={styles.quantityButton}
            onPress={() => updateQuantity(item.id, item.quantity + 1)}
          >
            <Text style={styles.quantityButtonText}>+</Text>
          </TouchableOpacity>
        </View>
      </View>
    </Card>
  );
  
  if (cartItems.length === 0) {
    return (
      <View style={styles.container}>
        <StatusBar
          barStyle="dark-content"
          backgroundColor={Colors.background.primary}
        />
        
        <View style={styles.emptyCart}>
          <Text style={styles.emptyCartEmoji}>🛒</Text>
          <Text style={styles.emptyCartTitle}>Your cart is empty</Text>
          <Text style={styles.emptyCartText}>
            Add some delicious food to get started
          </Text>
          <Button
            title="Browse Restaurants"
            onPress={() => navigation.navigate('Home')}
            style={styles.browseButton}
          />
        </View>
      </View>
    );
  }
  
  return (
    <View style={styles.container}>
      <StatusBar
        barStyle="dark-content"
        backgroundColor={Colors.background.primary}
      />
      
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Your Cart</Text>
        <TouchableOpacity onPress={() => setIsUrdu(!isUrdu)}>
          <Text style={styles.languageToggle}>
            {isUrdu ? 'EN' : 'اردو'}
          </Text>
        </TouchableOpacity>
      </View>
      
      <ScrollView style={styles.content}>
        {/* Delivery Address */}
        <Card style={styles.addressCard}>
          <View style={styles.addressHeader}>
            <Text style={styles.addressTitle}>Delivery Address</Text>
            <TouchableOpacity onPress={() => setShowAddressModal(true)}>
              <Text style={styles.changeAddressText}>Change</Text>
            </TouchableOpacity>
          </View>
          <Text style={styles.addressType}>{selectedAddress.type}</Text>
          <Text style={styles.addressText}>{selectedAddress.address}</Text>
        </Card>

        <View style={styles.cartItems}>
          {cartItems.map(renderCartItem)}
        </View>

        {/* Promo Code Section */}
        <Card style={styles.promoCard}>
          {!appliedPromo ? (
            <View>
              <View style={styles.promoHeader}>
                <Text style={styles.promoTitle}>Have a promo code?</Text>
                <TouchableOpacity onPress={() => setShowPromoInput(!showPromoInput)}>
                  <Text style={styles.promoToggle}>
                    {showPromoInput ? 'Cancel' : 'Apply'}
                  </Text>
                </TouchableOpacity>
              </View>

              {showPromoInput && (
                <View style={styles.promoInputContainer}>
                  <TextInput
                    style={styles.promoInput}
                    placeholder="Enter promo code"
                    value={promoCode}
                    onChangeText={setPromoCode}
                    autoCapitalize="characters"
                  />
                  <Button
                    title="Apply"
                    onPress={applyPromoCode}
                    style={styles.applyPromoButton}
                  />
                </View>
              )}
            </View>
          ) : (
            <View style={styles.appliedPromoContainer}>
              <View style={styles.appliedPromoInfo}>
                <Text style={styles.appliedPromoText}>
                  Promo Applied: {appliedPromo.type === 'percentage' ? `${appliedPromo.discount}% OFF` : `PKR ${appliedPromo.discount} OFF`}
                </Text>
                <Text style={styles.promoSavings}>You saved PKR {discount}</Text>
              </View>
              <TouchableOpacity onPress={removePromoCode}>
                <Text style={styles.removePromoText}>Remove</Text>
              </TouchableOpacity>
            </View>
          )}
        </Card>

        {/* Bill Summary */}
        <Card style={styles.billSummary}>
          <Text style={styles.billTitle}>Bill Summary</Text>

          <View style={styles.billRow}>
            <Text style={styles.billLabel}>Subtotal</Text>
            <Text style={styles.billValue}>PKR {subtotal}</Text>
          </View>

          <View style={styles.billRow}>
            <Text style={styles.billLabel}>Delivery Fee</Text>
            <Text style={styles.billValue}>PKR {deliveryFee}</Text>
          </View>

          <View style={styles.billRow}>
            <Text style={styles.billLabel}>Tax</Text>
            <Text style={styles.billValue}>PKR {tax}</Text>
          </View>

          {discount > 0 && (
            <View style={styles.billRow}>
              <Text style={[styles.billLabel, styles.discountLabel]}>Discount</Text>
              <Text style={[styles.billValue, styles.discountValue]}>-PKR {discount}</Text>
            </View>
          )}
          
          <View style={styles.billRow}>
            <Text style={styles.billLabel}>Tax (16%)</Text>
            <Text style={styles.billValue}>PKR {tax}</Text>
          </View>
          
          <View style={[styles.billRow, styles.billTotal]}>
            <Text style={styles.billTotalLabel}>Total</Text>
            <Text style={styles.billTotalValue}>PKR {total}</Text>
          </View>
        </Card>
      </ScrollView>
      
      <View style={styles.footer}>
        <Button
          title={`Proceed to Checkout • PKR ${total}`}
          onPress={handleCheckout}
          fullWidth
          size="large"
        />
      </View>

      {/* Address Selection Modal */}
      <Modal
        visible={showAddressModal}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setShowAddressModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Select Delivery Address</Text>
              <TouchableOpacity onPress={() => setShowAddressModal(false)}>
                <Text style={styles.closeButton}>×</Text>
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.modalBody}>
              {mockAddresses.map((address) => (
                <TouchableOpacity
                  key={address.id}
                  style={[
                    styles.addressOption,
                    selectedAddress.id === address.id && styles.selectedAddressOption,
                  ]}
                  onPress={() => {
                    setSelectedAddress(address);
                    setShowAddressModal(false);
                  }}
                >
                  <View style={styles.addressOptionContent}>
                    <Text style={styles.addressOptionType}>{address.type}</Text>
                    <Text style={styles.addressOptionText}>{address.address}</Text>
                    {address.isDefault && (
                      <Text style={styles.defaultAddressLabel}>Default</Text>
                    )}
                  </View>
                  {selectedAddress.id === address.id && (
                    <Text style={styles.selectedIndicator}>✓</Text>
                  )}
                </TouchableOpacity>
              ))}

              <TouchableOpacity style={styles.addAddressButton}>
                <Text style={styles.addAddressText}>+ Add New Address</Text>
              </TouchableOpacity>
            </ScrollView>
          </View>
        </View>
      </Modal>

      {/* Item Edit Modal */}
      <Modal
        visible={showItemModal}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setShowItemModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            {selectedItem && (
              <>
                <View style={styles.modalHeader}>
                  <Text style={styles.modalTitle}>Edit Item</Text>
                  <TouchableOpacity onPress={() => setShowItemModal(false)}>
                    <Text style={styles.closeButton}>×</Text>
                  </TouchableOpacity>
                </View>

                <View style={styles.modalBody}>
                  <Text style={styles.editItemName}>
                    {isUrdu ? selectedItem.nameUrdu : selectedItem.name}
                  </Text>
                  <Text style={styles.editItemNote}>
                    Item customization and special instructions will be available in future updates.
                  </Text>
                </View>

                <View style={styles.modalFooter}>
                  <Button
                    title="Close"
                    onPress={() => setShowItemModal(false)}
                    style={styles.closeModalButton}
                  />
                </View>
              </>
            )}
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background.primary,
  },
  
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: Spacing.lg,
    paddingTop: Spacing.lg,
    paddingBottom: Spacing.md,
  },
  
  headerTitle: {
    ...Typography.styles.h4,
    color: Colors.text.primary,
  },
  
  languageToggle: {
    ...Typography.styles.label,
    color: Colors.primary.main,
  },
  
  content: {
    flex: 1,
    paddingHorizontal: Spacing.lg,
  },
  
  // Address Card Styles
  addressCard: {
    margin: Spacing.lg,
    padding: Spacing.md,
  },

  addressHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.xs,
  },

  addressTitle: {
    ...Typography.styles.bodyBold,
    color: Colors.text.primary,
  },

  changeAddressText: {
    ...Typography.styles.body,
    color: Colors.primary.main,
  },

  addressType: {
    ...Typography.styles.caption,
    color: Colors.text.secondary,
    marginBottom: Spacing.xs,
  },

  addressText: {
    ...Typography.styles.body,
    color: Colors.text.primary,
  },

  cartItems: {
    marginBottom: Spacing.lg,
  },

  cartItem: {
    marginBottom: Spacing.md,
    padding: Spacing.md,
  },

  itemContent: {
    flex: 1,
  },
  
  itemHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.sm,
  },
  
  itemEmoji: {
    fontSize: 32,
    marginRight: Spacing.md,
  },
  
  itemInfo: {
    flex: 1,
  },
  
  itemName: {
    ...Typography.styles.label,
    color: Colors.text.primary,
    marginBottom: Spacing.xs,
  },
  
  itemRestaurant: {
    ...Typography.styles.caption,
    color: Colors.text.secondary,
    marginBottom: Spacing.xs,
  },
  
  itemPrice: {
    ...Typography.styles.priceSmall,
    color: Colors.primary.main,
  },

  itemCustomizations: {
    ...Typography.styles.caption,
    color: Colors.text.secondary,
    fontStyle: 'italic',
    marginBottom: Spacing.xs,
  },

  itemTotal: {
    ...Typography.styles.bodyBold,
    color: Colors.text.primary,
    marginTop: Spacing.xs,
  },

  itemActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },

  editButton: {
    padding: Spacing.xs,
    marginRight: Spacing.xs,
  },

  editButtonText: {
    fontSize: 16,
  },

  removeButton: {
    padding: Spacing.xs,
  },

  removeButtonText: {
    fontSize: 16,
  },

  quantityControls: {
    flexDirection: 'row',
    alignItems: 'center',
    alignSelf: 'flex-end',
  },
  
  quantityButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: Colors.primary.main,
    alignItems: 'center',
    justifyContent: 'center',
  },
  
  quantityButtonText: {
    ...Typography.styles.label,
    color: Colors.neutral.white,
    fontWeight: '600',
  },
  
  quantityText: {
    ...Typography.styles.label,
    color: Colors.text.primary,
    marginHorizontal: Spacing.md,
    minWidth: 24,
    textAlign: 'center',
  },
  
  billSummary: {
    padding: Spacing.lg,
    marginBottom: Spacing.lg,
  },
  
  billTitle: {
    ...Typography.styles.h6,
    color: Colors.text.primary,
    marginBottom: Spacing.md,
  },
  
  billRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.sm,
  },
  
  billLabel: {
    ...Typography.styles.body,
    color: Colors.text.secondary,
  },
  
  billValue: {
    ...Typography.styles.body,
    color: Colors.text.primary,
  },
  
  billTotal: {
    borderTopWidth: 1,
    borderTopColor: Colors.border.light,
    paddingTop: Spacing.sm,
    marginTop: Spacing.sm,
  },
  
  billTotalLabel: {
    ...Typography.styles.label,
    color: Colors.text.primary,
    fontWeight: '600',
  },
  
  billTotalValue: {
    ...Typography.styles.price,
    color: Colors.primary.main,
  },
  
  footer: {
    padding: Spacing.lg,
    backgroundColor: Colors.background.primary,
    borderTopWidth: 1,
    borderTopColor: Colors.border.light,
  },
  
  // Empty cart styles
  emptyCart: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: Spacing.xl,
  },
  
  emptyCartEmoji: {
    fontSize: 80,
    marginBottom: Spacing.lg,
  },
  
  emptyCartTitle: {
    ...Typography.styles.h4,
    color: Colors.text.primary,
    marginBottom: Spacing.sm,
  },
  
  emptyCartText: {
    ...Typography.styles.body,
    color: Colors.text.secondary,
    textAlign: 'center',
    marginBottom: Spacing.xl,
  },
  
  browseButton: {
    paddingHorizontal: Spacing.xl,
  },

  // Promo Code Styles
  promoCard: {
    margin: Spacing.lg,
    padding: Spacing.md,
  },

  promoHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.sm,
  },

  promoTitle: {
    ...Typography.styles.bodyBold,
    color: Colors.text.primary,
  },

  promoToggle: {
    ...Typography.styles.body,
    color: Colors.primary.main,
  },

  promoInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: Spacing.sm,
  },

  promoInput: {
    flex: 1,
    borderWidth: 1,
    borderColor: Colors.border.light,
    borderRadius: Spacing.radius.md,
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
    marginRight: Spacing.sm,
    ...Typography.styles.body,
  },

  applyPromoButton: {
    paddingHorizontal: Spacing.lg,
  },

  appliedPromoContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },

  appliedPromoInfo: {
    flex: 1,
  },

  appliedPromoText: {
    ...Typography.styles.bodyBold,
    color: Colors.success.main,
  },

  promoSavings: {
    ...Typography.styles.caption,
    color: Colors.success.main,
  },

  removePromoText: {
    ...Typography.styles.body,
    color: Colors.error.main,
  },

  discountLabel: {
    color: Colors.success.main,
  },

  discountValue: {
    color: Colors.success.main,
  },

  // Modal Styles
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },

  modalContent: {
    backgroundColor: Colors.background.primary,
    borderTopLeftRadius: Spacing.radius.xl,
    borderTopRightRadius: Spacing.radius.xl,
    maxHeight: '80%',
  },

  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: Spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border.light,
  },

  modalTitle: {
    ...Typography.styles.h5,
    color: Colors.text.primary,
  },

  closeButton: {
    fontSize: 24,
    color: Colors.text.secondary,
    padding: Spacing.xs,
  },

  modalBody: {
    padding: Spacing.lg,
  },

  modalFooter: {
    padding: Spacing.lg,
    borderTopWidth: 1,
    borderTopColor: Colors.border.light,
  },

  // Address Modal Styles
  addressOption: {
    padding: Spacing.md,
    borderWidth: 1,
    borderColor: Colors.border.light,
    borderRadius: Spacing.radius.md,
    marginBottom: Spacing.sm,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },

  selectedAddressOption: {
    borderColor: Colors.primary.main,
    backgroundColor: Colors.primary.main + '10',
  },

  addressOptionContent: {
    flex: 1,
  },

  addressOptionType: {
    ...Typography.styles.bodyBold,
    color: Colors.text.primary,
    marginBottom: Spacing.xs,
  },

  addressOptionText: {
    ...Typography.styles.body,
    color: Colors.text.secondary,
  },

  defaultAddressLabel: {
    ...Typography.styles.caption,
    color: Colors.primary.main,
    marginTop: Spacing.xs,
  },

  selectedIndicator: {
    ...Typography.styles.bodyBold,
    color: Colors.primary.main,
    fontSize: 18,
  },

  addAddressButton: {
    padding: Spacing.md,
    borderWidth: 1,
    borderColor: Colors.primary.main,
    borderRadius: Spacing.radius.md,
    borderStyle: 'dashed',
    alignItems: 'center',
    marginTop: Spacing.sm,
  },

  addAddressText: {
    ...Typography.styles.body,
    color: Colors.primary.main,
  },

  // Item Edit Modal Styles
  editItemName: {
    ...Typography.styles.h5,
    color: Colors.text.primary,
    marginBottom: Spacing.md,
  },

  editItemNote: {
    ...Typography.styles.body,
    color: Colors.text.secondary,
    textAlign: 'center',
    fontStyle: 'italic',
  },

  closeModalButton: {
    backgroundColor: Colors.background.secondary,
  },
});

export default CartScreen;
