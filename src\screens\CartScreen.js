import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  StatusBar,
} from 'react-native';
import { Colors, Typography, Spacing, AppConfig } from '../constants';
import Button from '../components/common/Button';
import Card from '../components/common/Card';

// Mock cart data
const mockCartItems = [
  {
    id: '1',
    name: 'Chicken Biryani',
    nameUrdu: 'چکن بریانی',
    price: 450,
    quantity: 2,
    restaurant: 'Karachi Biryani House',
    image: '🍛',
  },
  {
    id: '2',
    name: 'Margherita Pizza',
    nameUrdu: 'مارگریٹا پیزا',
    price: 850,
    quantity: 1,
    restaurant: 'Pizza Palace',
    image: '🍕',
  },
];

const CartScreen = ({ navigation }) => {
  const [cartItems, setCartItems] = useState(mockCartItems);
  const [isUrdu, setIsUrdu] = useState(false);
  
  const subtotal = cartItems.reduce((sum, item) => sum + (item.price * item.quantity), 0);
  const deliveryFee = AppConfig.defaults.deliveryFee;
  const tax = Math.round(subtotal * AppConfig.defaults.taxRate);
  const total = subtotal + deliveryFee + tax;
  
  const updateQuantity = (itemId, newQuantity) => {
    if (newQuantity === 0) {
      setCartItems(items => items.filter(item => item.id !== itemId));
    } else {
      setCartItems(items =>
        items.map(item =>
          item.id === itemId ? { ...item, quantity: newQuantity } : item
        )
      );
    }
  };
  
  const handleCheckout = () => {
    navigation.navigate('Checkout', { cartItems, total });
  };
  
  const renderCartItem = (item) => (
    <Card key={item.id} style={styles.cartItem}>
      <View style={styles.itemHeader}>
        <Text style={styles.itemEmoji}>{item.image}</Text>
        <View style={styles.itemInfo}>
          <Text style={styles.itemName}>
            {isUrdu ? item.nameUrdu : item.name}
          </Text>
          <Text style={styles.itemRestaurant}>{item.restaurant}</Text>
          <Text style={styles.itemPrice}>PKR {item.price}</Text>
        </View>
      </View>
      
      <View style={styles.quantityControls}>
        <TouchableOpacity
          style={styles.quantityButton}
          onPress={() => updateQuantity(item.id, item.quantity - 1)}
        >
          <Text style={styles.quantityButtonText}>−</Text>
        </TouchableOpacity>
        
        <Text style={styles.quantityText}>{item.quantity}</Text>
        
        <TouchableOpacity
          style={styles.quantityButton}
          onPress={() => updateQuantity(item.id, item.quantity + 1)}
        >
          <Text style={styles.quantityButtonText}>+</Text>
        </TouchableOpacity>
      </View>
    </Card>
  );
  
  if (cartItems.length === 0) {
    return (
      <View style={styles.container}>
        <StatusBar
          barStyle="dark-content"
          backgroundColor={Colors.background.primary}
        />
        
        <View style={styles.emptyCart}>
          <Text style={styles.emptyCartEmoji}>🛒</Text>
          <Text style={styles.emptyCartTitle}>Your cart is empty</Text>
          <Text style={styles.emptyCartText}>
            Add some delicious food to get started
          </Text>
          <Button
            title="Browse Restaurants"
            onPress={() => navigation.navigate('Home')}
            style={styles.browseButton}
          />
        </View>
      </View>
    );
  }
  
  return (
    <View style={styles.container}>
      <StatusBar
        barStyle="dark-content"
        backgroundColor={Colors.background.primary}
      />
      
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Your Cart</Text>
        <TouchableOpacity onPress={() => setIsUrdu(!isUrdu)}>
          <Text style={styles.languageToggle}>
            {isUrdu ? 'EN' : 'اردو'}
          </Text>
        </TouchableOpacity>
      </View>
      
      <ScrollView style={styles.content}>
        <View style={styles.cartItems}>
          {cartItems.map(renderCartItem)}
        </View>
        
        {/* Bill Summary */}
        <Card style={styles.billSummary}>
          <Text style={styles.billTitle}>Bill Summary</Text>
          
          <View style={styles.billRow}>
            <Text style={styles.billLabel}>Subtotal</Text>
            <Text style={styles.billValue}>PKR {subtotal}</Text>
          </View>
          
          <View style={styles.billRow}>
            <Text style={styles.billLabel}>Delivery Fee</Text>
            <Text style={styles.billValue}>PKR {deliveryFee}</Text>
          </View>
          
          <View style={styles.billRow}>
            <Text style={styles.billLabel}>Tax (16%)</Text>
            <Text style={styles.billValue}>PKR {tax}</Text>
          </View>
          
          <View style={[styles.billRow, styles.billTotal]}>
            <Text style={styles.billTotalLabel}>Total</Text>
            <Text style={styles.billTotalValue}>PKR {total}</Text>
          </View>
        </Card>
      </ScrollView>
      
      <View style={styles.footer}>
        <Button
          title={`Proceed to Checkout • PKR ${total}`}
          onPress={handleCheckout}
          fullWidth
          size="large"
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background.primary,
  },
  
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: Spacing.lg,
    paddingTop: Spacing.lg,
    paddingBottom: Spacing.md,
  },
  
  headerTitle: {
    ...Typography.styles.h4,
    color: Colors.text.primary,
  },
  
  languageToggle: {
    ...Typography.styles.label,
    color: Colors.primary.main,
  },
  
  content: {
    flex: 1,
    paddingHorizontal: Spacing.lg,
  },
  
  cartItems: {
    marginBottom: Spacing.lg,
  },
  
  cartItem: {
    marginBottom: Spacing.md,
    padding: Spacing.md,
  },
  
  itemHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.sm,
  },
  
  itemEmoji: {
    fontSize: 32,
    marginRight: Spacing.md,
  },
  
  itemInfo: {
    flex: 1,
  },
  
  itemName: {
    ...Typography.styles.label,
    color: Colors.text.primary,
    marginBottom: Spacing.xs,
  },
  
  itemRestaurant: {
    ...Typography.styles.caption,
    color: Colors.text.secondary,
    marginBottom: Spacing.xs,
  },
  
  itemPrice: {
    ...Typography.styles.priceSmall,
    color: Colors.primary.main,
  },
  
  quantityControls: {
    flexDirection: 'row',
    alignItems: 'center',
    alignSelf: 'flex-end',
  },
  
  quantityButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: Colors.primary.main,
    alignItems: 'center',
    justifyContent: 'center',
  },
  
  quantityButtonText: {
    ...Typography.styles.label,
    color: Colors.neutral.white,
    fontWeight: '600',
  },
  
  quantityText: {
    ...Typography.styles.label,
    color: Colors.text.primary,
    marginHorizontal: Spacing.md,
    minWidth: 24,
    textAlign: 'center',
  },
  
  billSummary: {
    padding: Spacing.lg,
    marginBottom: Spacing.lg,
  },
  
  billTitle: {
    ...Typography.styles.h6,
    color: Colors.text.primary,
    marginBottom: Spacing.md,
  },
  
  billRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.sm,
  },
  
  billLabel: {
    ...Typography.styles.body,
    color: Colors.text.secondary,
  },
  
  billValue: {
    ...Typography.styles.body,
    color: Colors.text.primary,
  },
  
  billTotal: {
    borderTopWidth: 1,
    borderTopColor: Colors.border.light,
    paddingTop: Spacing.sm,
    marginTop: Spacing.sm,
  },
  
  billTotalLabel: {
    ...Typography.styles.label,
    color: Colors.text.primary,
    fontWeight: '600',
  },
  
  billTotalValue: {
    ...Typography.styles.price,
    color: Colors.primary.main,
  },
  
  footer: {
    padding: Spacing.lg,
    backgroundColor: Colors.background.primary,
    borderTopWidth: 1,
    borderTopColor: Colors.border.light,
  },
  
  // Empty cart styles
  emptyCart: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: Spacing.xl,
  },
  
  emptyCartEmoji: {
    fontSize: 80,
    marginBottom: Spacing.lg,
  },
  
  emptyCartTitle: {
    ...Typography.styles.h4,
    color: Colors.text.primary,
    marginBottom: Spacing.sm,
  },
  
  emptyCartText: {
    ...Typography.styles.body,
    color: Colors.text.secondary,
    textAlign: 'center',
    marginBottom: Spacing.xl,
  },
  
  browseButton: {
    paddingHorizontal: Spacing.xl,
  },
});

export default CartScreen;
