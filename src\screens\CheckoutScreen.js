import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  StatusBar,
} from 'react-native';
import { Colors, Typography, Spacing, AppConfig } from '../constants';
import Card from '../components/common/Card';
import Button from '../components/common/Button';
import Input from '../components/common/Input';

const CheckoutScreen = ({ navigation, route }) => {
  const { cartItems, total } = route.params || {};
  const [selectedPayment, setSelectedPayment] = useState('cash');
  const [deliveryAddress, setDeliveryAddress] = useState('');
  const [deliveryInstructions, setDeliveryInstructions] = useState('');
  const [selectedTime, setSelectedTime] = useState('asap');
  
  const handlePlaceOrder = () => {
    // Navigate to order tracking
    navigation.navigate('OrderTracking', {
      orderId: 'ORD-' + Date.now(),
      total,
    });
  };
  
  return (
    <View style={styles.container}>
      <StatusBar
        barStyle="dark-content"
        backgroundColor={Colors.background.primary}
      />
      
      <ScrollView style={styles.content}>
        {/* Delivery Address */}
        <Card style={styles.section}>
          <Text style={styles.sectionTitle}>Delivery Address</Text>
          <Input
            placeholder="Enter your delivery address"
            value={deliveryAddress}
            onChangeText={setDeliveryAddress}
            multiline
            numberOfLines={3}
            leftIcon={<Text style={styles.inputIcon}>📍</Text>}
          />
        </Card>
        
        {/* Payment Method */}
        <Card style={styles.section}>
          <Text style={styles.sectionTitle}>Payment Method</Text>
          {AppConfig.paymentMethods
            .filter(method => method.enabled)
            .map((method) => (
              <TouchableOpacity
                key={method.id}
                style={[
                  styles.paymentOption,
                  selectedPayment === method.id && styles.paymentOptionSelected,
                ]}
                onPress={() => setSelectedPayment(method.id)}
              >
                <View style={styles.paymentInfo}>
                  <Text style={styles.paymentIcon}>
                    {method.id === 'cash' ? '💵' : '📱'}
                  </Text>
                  <Text style={styles.paymentName}>{method.name}</Text>
                </View>
                <View
                  style={[
                    styles.radioButton,
                    selectedPayment === method.id && styles.radioButtonSelected,
                  ]}
                >
                  {selectedPayment === method.id && (
                    <View style={styles.radioButtonInner} />
                  )}
                </View>
              </TouchableOpacity>
            ))}
        </Card>
        
        {/* Delivery Time */}
        <Card style={styles.section}>
          <Text style={styles.sectionTitle}>Delivery Time</Text>
          <TouchableOpacity
            style={[
              styles.timeOption,
              selectedTime === 'asap' && styles.timeOptionSelected,
            ]}
            onPress={() => setSelectedTime('asap')}
          >
            <Text style={styles.timeText}>As soon as possible</Text>
            <Text style={styles.timeSubtext}>25-35 minutes</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[
              styles.timeOption,
              selectedTime === 'scheduled' && styles.timeOptionSelected,
            ]}
            onPress={() => setSelectedTime('scheduled')}
          >
            <Text style={styles.timeText}>Schedule for later</Text>
            <Text style={styles.timeSubtext}>Choose specific time</Text>
          </TouchableOpacity>
        </Card>
        
        {/* Delivery Instructions */}
        <Card style={styles.section}>
          <Text style={styles.sectionTitle}>Delivery Instructions (Optional)</Text>
          <Input
            placeholder="Any special instructions for delivery..."
            value={deliveryInstructions}
            onChangeText={setDeliveryInstructions}
            multiline
            numberOfLines={2}
            leftIcon={<Text style={styles.inputIcon}>📝</Text>}
          />
        </Card>
        
        {/* Order Summary */}
        <Card style={styles.section}>
          <Text style={styles.sectionTitle}>Order Summary</Text>
          <View style={styles.summaryRow}>
            <Text style={styles.summaryLabel}>Total Amount</Text>
            <Text style={styles.summaryValue}>PKR {total}</Text>
          </View>
          <View style={styles.summaryRow}>
            <Text style={styles.summaryLabel}>Payment Method</Text>
            <Text style={styles.summaryValue}>
              {AppConfig.paymentMethods.find(m => m.id === selectedPayment)?.name}
            </Text>
          </View>
        </Card>
      </ScrollView>
      
      <View style={styles.footer}>
        <Button
          title={`Place Order • PKR ${total}`}
          onPress={handlePlaceOrder}
          fullWidth
          size="large"
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background.primary,
  },
  
  content: {
    flex: 1,
    paddingHorizontal: Spacing.lg,
    paddingTop: Spacing.md,
  },
  
  section: {
    marginBottom: Spacing.lg,
    padding: Spacing.lg,
  },
  
  sectionTitle: {
    ...Typography.styles.h6,
    color: Colors.text.primary,
    marginBottom: Spacing.md,
  },
  
  inputIcon: {
    fontSize: 16,
  },
  
  paymentOption: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: Spacing.md,
    paddingHorizontal: Spacing.sm,
    borderRadius: Spacing.radius.md,
    marginBottom: Spacing.sm,
    borderWidth: 1,
    borderColor: Colors.border.light,
  },
  
  paymentOptionSelected: {
    borderColor: Colors.primary.main,
    backgroundColor: Colors.primary[50],
  },
  
  paymentInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  
  paymentIcon: {
    fontSize: 20,
    marginRight: Spacing.sm,
  },
  
  paymentName: {
    ...Typography.styles.body,
    color: Colors.text.primary,
  },
  
  radioButton: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: Colors.border.medium,
    alignItems: 'center',
    justifyContent: 'center',
  },
  
  radioButtonSelected: {
    borderColor: Colors.primary.main,
  },
  
  radioButtonInner: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: Colors.primary.main,
  },
  
  timeOption: {
    paddingVertical: Spacing.md,
    paddingHorizontal: Spacing.sm,
    borderRadius: Spacing.radius.md,
    marginBottom: Spacing.sm,
    borderWidth: 1,
    borderColor: Colors.border.light,
  },
  
  timeOptionSelected: {
    borderColor: Colors.primary.main,
    backgroundColor: Colors.primary[50],
  },
  
  timeText: {
    ...Typography.styles.body,
    color: Colors.text.primary,
    marginBottom: Spacing.xs,
  },
  
  timeSubtext: {
    ...Typography.styles.caption,
    color: Colors.text.secondary,
  },
  
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.sm,
  },
  
  summaryLabel: {
    ...Typography.styles.body,
    color: Colors.text.secondary,
  },
  
  summaryValue: {
    ...Typography.styles.body,
    color: Colors.text.primary,
    fontWeight: '600',
  },
  
  footer: {
    padding: Spacing.lg,
    backgroundColor: Colors.background.primary,
    borderTopWidth: 1,
    borderTopColor: Colors.border.light,
  },
});

export default CheckoutScreen;
