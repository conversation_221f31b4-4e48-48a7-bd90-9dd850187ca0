import { useRef, useState } from 'react';
import {
    FlatList,
    RefreshControl,
    ScrollView,
    StatusBar,
    StyleSheet,
    Text,
    TouchableOpacity,
    View
} from 'react-native';
import Card from '../components/common/Card';
import { AppConfig, Colors, Spacing, Typography } from '../constants';

// Mock data - replace with API calls
const mockPromos = [
  {
    id: '1',
    title: 'Free Delivery',
    subtitle: 'On orders above PKR 500',
    image: '🚚',
    backgroundColor: Colors.primary.main,
  },
  {
    id: '2',
    title: '20% Off',
    subtitle: 'First order discount',
    image: '🎉',
    backgroundColor: Colors.secondary.main,
  },
  {
    id: '3',
    title: 'Weekend Special',
    subtitle: 'Buy 1 Get 1 Free',
    image: '🍕',
    backgroundColor: Colors.accent.main,
  },
];

const mockRestaurants = [
  {
    id: '1',
    name: 'Karachi Biryani House',
    nameUrdu: 'کراچی بریانی ہاؤس',
    cuisine: 'Pakistani',
    rating: 4.5,
    deliveryTime: '25-35 min',
    deliveryFee: 49,
    image: '🍛',
    featured: true,
    isOpen: true,
    distance: '1.2 km',
    totalOrders: '1000+',
    specialOffer: 'Free delivery on orders above PKR 500',
  },
  {
    id: '2',
    name: 'Pizza Palace',
    nameUrdu: 'پیزا پیلس',
    cuisine: 'Italian',
    rating: 4.2,
    deliveryTime: '30-40 min',
    deliveryFee: 59,
    image: '🍕',
    featured: true,
    isOpen: true,
    distance: '2.1 km',
    totalOrders: '800+',
    specialOffer: '20% off on large pizzas',
  },
  {
    id: '3',
    name: 'BBQ Tonight',
    nameUrdu: 'بی بی کیو ٹونائٹ',
    cuisine: 'BBQ',
    rating: 4.7,
    deliveryTime: '20-30 min',
    deliveryFee: 39,
    image: '🍖',
    featured: false,
    isOpen: true,
    distance: '0.8 km',
    totalOrders: '1200+',
    specialOffer: 'Buy 1 Get 1 Free on selected items',
  },
  {
    id: '4',
    name: 'Subway',
    nameUrdu: 'سب وے',
    cuisine: 'Fast Food',
    rating: 4.0,
    deliveryTime: '15-25 min',
    deliveryFee: 29,
    image: '🥪',
    featured: false,
    isOpen: true,
    distance: '1.5 km',
    totalOrders: '600+',
    specialOffer: null,
  },
  {
    id: '5',
    name: 'KFC',
    nameUrdu: 'کے ایف سی',
    cuisine: 'Fast Food',
    rating: 4.3,
    deliveryTime: '20-30 min',
    deliveryFee: 49,
    image: '🍗',
    featured: true,
    isOpen: false,
    distance: '2.3 km',
    totalOrders: '2000+',
    specialOffer: 'Family feast deals available',
  },
  {
    id: '6',
    name: 'Chai Shai',
    nameUrdu: 'چائے شائے',
    cuisine: 'Beverages',
    rating: 4.1,
    deliveryTime: '10-20 min',
    deliveryFee: 19,
    image: '☕',
    featured: false,
    isOpen: true,
    distance: '0.5 km',
    totalOrders: '400+',
    specialOffer: 'Free cookies with tea orders',
  },
];

const HomeScreen = ({ navigation }) => {
  const [selectedCity, setSelectedCity] = useState(AppConfig.cities[0]);
  const [refreshing, setRefreshing] = useState(false);
  const [isUrdu, setIsUrdu] = useState(false);
  const promoScrollRef = useRef(null);
  
  const onRefresh = async () => {
    setRefreshing(true);
    // Simulate API refresh
    await new Promise(resolve => setTimeout(resolve, 1000));
    setRefreshing(false);
  };
  
  const handleLocationPress = () => {
    // Open location selector modal
    console.log('Open location selector');
  };
  
  const handleCategoryPress = (category) => {
    navigation.navigate('Search', { category: category.id });
  };
  
  const handleRestaurantPress = (restaurant) => {
    navigation.navigate('Restaurant', { restaurantId: restaurant.id });
  };
  
  const renderPromoItem = ({ item }) => (
    <Card
      style={[styles.promoCard, { backgroundColor: item.backgroundColor }]}
      onPress={() => console.log('Promo pressed:', item.id)}
    >
      <View style={styles.promoContent}>
        <Text style={styles.promoEmoji}>{item.image}</Text>
        <View style={styles.promoText}>
          <Text style={styles.promoTitle}>{item.title}</Text>
          <Text style={styles.promoSubtitle}>{item.subtitle}</Text>
        </View>
      </View>
    </Card>
  );
  
  const renderCategoryItem = ({ item }) => (
    <TouchableOpacity
      style={styles.categoryItem}
      onPress={() => handleCategoryPress(item)}
    >
      <View style={[styles.categoryIcon, { backgroundColor: item.color + '20' }]}>
        <Text style={styles.categoryEmoji}>{item.icon}</Text>
      </View>
      <Text style={styles.categoryName}>
        {isUrdu ? item.nameUrdu : item.name}
      </Text>
    </TouchableOpacity>
  );
  
  const renderRestaurantItem = ({ item }) => (
    <Card
      style={[
        styles.restaurantCard,
        !item.isOpen && styles.restaurantCardClosed,
      ]}
      onPress={() => handleRestaurantPress(item)}
    >
      <View style={styles.restaurantImage}>
        <Text style={styles.restaurantEmoji}>{item.image}</Text>
        {item.featured && (
          <View style={styles.featuredBadge}>
            <Text style={styles.featuredText}>Featured</Text>
          </View>
        )}
        {!item.isOpen && (
          <View style={styles.closedOverlay}>
            <Text style={styles.closedText}>Closed</Text>
          </View>
        )}
      </View>

      <View style={styles.restaurantInfo}>
        <View style={styles.restaurantHeader}>
          <Text style={styles.restaurantName}>
            {isUrdu ? item.nameUrdu : item.name}
          </Text>
          <Text style={styles.restaurantDistance}>{item.distance}</Text>
        </View>

        <Text style={styles.restaurantCuisine}>{item.cuisine} • {item.totalOrders} orders</Text>

        {item.specialOffer && (
          <Text style={styles.specialOffer}>🎉 {item.specialOffer}</Text>
        )}

        <View style={styles.restaurantMeta}>
          <View style={styles.ratingContainer}>
            <Text style={styles.ratingText}>⭐ {item.rating}</Text>
          </View>
          <Text style={styles.deliveryTime}>🕒 {item.deliveryTime}</Text>
          <Text style={styles.deliveryFee}>
            {item.deliveryFee === 0 ? '🚚 Free' : `🚚 PKR ${item.deliveryFee}`}
          </Text>
        </View>
      </View>
    </Card>
  );
  
  return (
    <View style={styles.container}>
      <StatusBar
        barStyle="dark-content"
        backgroundColor={Colors.background.primary}
      />
      
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.locationButton}
            onPress={handleLocationPress}
          >
            <Text style={styles.locationIcon}>📍</Text>
            <View style={styles.locationText}>
              <Text style={styles.locationLabel}>Deliver to</Text>
              <Text style={styles.locationValue}>
                {isUrdu ? selectedCity.nameUrdu : selectedCity.name}
              </Text>
            </View>
            <Text style={styles.locationArrow}>▼</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.languageToggle}
            onPress={() => setIsUrdu(!isUrdu)}
          >
            <Text style={styles.languageToggleText}>
              {isUrdu ? 'EN' : 'اردو'}
            </Text>
          </TouchableOpacity>
        </View>

        {/* Search Bar */}
        <TouchableOpacity
          style={styles.searchBar}
          onPress={() => navigation.navigate('Search')}
        >
          <Text style={styles.searchIcon}>🔍</Text>
          <Text style={styles.searchPlaceholder}>
            {isUrdu ? 'ریستوراں یا کھانا تلاش کریں' : 'Search restaurants or food'}
          </Text>
        </TouchableOpacity>
        
        {/* Promo Banner */}
        <View style={styles.section}>
          <FlatList
            ref={promoScrollRef}
            data={mockPromos}
            renderItem={renderPromoItem}
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.promoList}
            snapToInterval={280}
            decelerationRate="fast"
          />
        </View>
        
        {/* Food Categories */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>
            {isUrdu ? 'کھانے کی اقسام' : 'Food Categories'}
          </Text>
          <FlatList
            data={AppConfig.foodCategories}
            renderItem={renderCategoryItem}
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.categoryList}
          />
        </View>
        
        {/* Featured Restaurants */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>
              {isUrdu ? 'نمایاں ریستوراں' : 'Featured Restaurants'}
            </Text>
            <TouchableOpacity onPress={() => navigation.navigate('Search')}>
              <Text style={styles.seeAllText}>
                {isUrdu ? 'سب دیکھیں' : 'See All'}
              </Text>
            </TouchableOpacity>
          </View>
          
          <FlatList
            data={mockRestaurants.filter(r => r.featured)}
            renderItem={renderRestaurantItem}
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.restaurantList}
          />
        </View>
        
        {/* All Restaurants */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>
            {isUrdu ? 'تمام ریستوراں' : 'All Restaurants'}
          </Text>
          
          {mockRestaurants.map((restaurant) => (
            <View key={restaurant.id} style={styles.restaurantListItem}>
              {renderRestaurantItem({ item: restaurant })}
            </View>
          ))}
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background.primary,
  },

  scrollView: {
    flex: 1,
  },

  scrollContent: {
    paddingBottom: Spacing.xl,
  },

  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: Spacing.lg,
    paddingTop: Spacing.lg,
    paddingBottom: Spacing.md,
  },

  locationButton: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },

  locationIcon: {
    fontSize: 20,
    marginRight: Spacing.sm,
  },

  locationText: {
    flex: 1,
  },

  locationLabel: {
    ...Typography.styles.caption,
    color: Colors.text.tertiary,
  },

  locationValue: {
    ...Typography.styles.label,
    color: Colors.text.primary,
  },

  locationArrow: {
    ...Typography.styles.caption,
    color: Colors.text.tertiary,
    marginLeft: Spacing.xs,
  },

  languageToggle: {
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs,
    borderRadius: Spacing.radius.sm,
    backgroundColor: Colors.background.secondary,
  },

  languageToggleText: {
    ...Typography.styles.labelSmall,
    color: Colors.text.primary,
    fontWeight: '600',
  },

  searchBar: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.background.secondary,
    marginHorizontal: Spacing.lg,
    marginBottom: Spacing.md,
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
    borderRadius: Spacing.radius.lg,
    borderWidth: 1,
    borderColor: Colors.border.light,
  },

  searchIcon: {
    fontSize: 16,
    marginRight: Spacing.sm,
  },

  searchPlaceholder: {
    ...Typography.styles.body,
    color: Colors.text.secondary,
    flex: 1,
  },

  section: {
    marginBottom: Spacing.lg,
  },

  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: Spacing.lg,
    marginBottom: Spacing.md,
  },

  sectionTitle: {
    ...Typography.styles.h5,
    color: Colors.text.primary,
    paddingHorizontal: Spacing.lg,
    marginBottom: Spacing.md,
  },

  seeAllText: {
    ...Typography.styles.label,
    color: Colors.primary.main,
  },

  // Promo styles
  promoList: {
    paddingHorizontal: Spacing.lg,
    gap: Spacing.md,
  },

  promoCard: {
    width: 280,
    height: 120,
    marginRight: Spacing.md,
  },

  promoContent: {
    flexDirection: 'row',
    alignItems: 'center',
    height: '100%',
    paddingHorizontal: Spacing.lg,
  },

  promoEmoji: {
    fontSize: 40,
    marginRight: Spacing.md,
  },

  promoText: {
    flex: 1,
  },

  promoTitle: {
    ...Typography.styles.h6,
    color: Colors.neutral.white,
    marginBottom: Spacing.xs,
  },

  promoSubtitle: {
    ...Typography.styles.bodySmall,
    color: Colors.neutral.white,
    opacity: 0.9,
  },

  // Category styles
  categoryList: {
    paddingHorizontal: Spacing.lg,
    gap: Spacing.md,
  },

  categoryItem: {
    alignItems: 'center',
    width: 80,
  },

  categoryIcon: {
    width: 60,
    height: 60,
    borderRadius: 30,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: Spacing.xs,
  },

  categoryEmoji: {
    fontSize: 24,
  },

  categoryName: {
    ...Typography.styles.caption,
    color: Colors.text.primary,
    textAlign: 'center',
  },

  // Restaurant styles
  restaurantList: {
    paddingHorizontal: Spacing.lg,
    gap: Spacing.md,
  },

  restaurantListItem: {
    marginHorizontal: Spacing.lg,
    marginBottom: Spacing.md,
  },

  restaurantCard: {
    width: 280,
    marginRight: Spacing.md,
  },

  restaurantCardClosed: {
    opacity: 0.7,
  },

  restaurantImage: {
    height: 120,
    backgroundColor: Colors.background.secondary,
    borderRadius: Spacing.radius.md,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: Spacing.sm,
    position: 'relative',
  },

  restaurantEmoji: {
    fontSize: 40,
  },

  featuredBadge: {
    position: 'absolute',
    top: Spacing.sm,
    right: Spacing.sm,
    backgroundColor: Colors.primary.main,
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs,
    borderRadius: Spacing.radius.sm,
  },

  featuredText: {
    ...Typography.styles.caption,
    color: Colors.neutral.white,
    fontWeight: '600',
  },

  closedOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: Spacing.radius.md,
  },

  closedText: {
    ...Typography.styles.bodyBold,
    color: Colors.background.primary,
  },

  restaurantInfo: {
    paddingHorizontal: Spacing.sm,
  },

  restaurantHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: Spacing.xs,
  },

  restaurantName: {
    ...Typography.styles.label,
    color: Colors.text.primary,
    flex: 1,
  },

  restaurantDistance: {
    ...Typography.styles.caption,
    color: Colors.text.secondary,
  },

  specialOffer: {
    ...Typography.styles.caption,
    color: Colors.accent.main,
    backgroundColor: Colors.accent.main + '15',
    paddingHorizontal: Spacing.xs,
    paddingVertical: 2,
    borderRadius: Spacing.radius.sm,
    marginBottom: Spacing.xs,
    overflow: 'hidden',
  },

  restaurantCuisine: {
    ...Typography.styles.caption,
    color: Colors.text.secondary,
    marginBottom: Spacing.sm,
  },

  restaurantMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.sm,
  },

  ratingContainer: {
    backgroundColor: Colors.success.bg,
    paddingHorizontal: Spacing.xs,
    paddingVertical: 2,
    borderRadius: Spacing.radius.sm,
  },

  ratingText: {
    ...Typography.styles.caption,
    color: Colors.success.main,
    fontWeight: '600',
  },

  deliveryTime: {
    ...Typography.styles.caption,
    color: Colors.text.tertiary,
  },

  deliveryFee: {
    ...Typography.styles.caption,
    color: Colors.text.tertiary,
  },
});

export default HomeScreen;
