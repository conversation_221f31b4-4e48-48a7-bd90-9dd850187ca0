import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  FlatList,
  Image,
  StatusBar,
  RefreshControl,
} from 'react-native';
import { Colors, Typography, Spacing, AppConfig } from '../constants';
import Card from '../components/common/Card';
import Button from '../components/common/Button';

// Mock data - replace with API calls
const mockPromos = [
  {
    id: '1',
    title: 'Free Delivery',
    subtitle: 'On orders above PKR 500',
    image: '🚚',
    backgroundColor: Colors.primary.main,
  },
  {
    id: '2',
    title: '20% Off',
    subtitle: 'First order discount',
    image: '🎉',
    backgroundColor: Colors.secondary.main,
  },
  {
    id: '3',
    title: 'Weekend Special',
    subtitle: 'Buy 1 Get 1 Free',
    image: '🍕',
    backgroundColor: Colors.accent.main,
  },
];

const mockRestaurants = [
  {
    id: '1',
    name: 'Karachi Biryani House',
    nameUrdu: 'کراچی بریانی ہاؤس',
    cuisine: 'Pakistani',
    rating: 4.5,
    deliveryTime: '25-35 min',
    deliveryFee: 49,
    image: '🍛',
    featured: true,
  },
  {
    id: '2',
    name: 'Pizza Palace',
    nameUrdu: 'پیزا پیلس',
    cuisine: 'Italian',
    rating: 4.2,
    deliveryTime: '30-40 min',
    deliveryFee: 59,
    image: '🍕',
    featured: true,
  },
  {
    id: '3',
    name: 'BBQ Tonight',
    nameUrdu: 'بی بی کیو ٹونائٹ',
    cuisine: 'BBQ',
    rating: 4.7,
    deliveryTime: '20-30 min',
    deliveryFee: 39,
    image: '🍖',
    featured: false,
  },
];

const HomeScreen = ({ navigation }) => {
  const [selectedCity, setSelectedCity] = useState(AppConfig.cities[0]);
  const [refreshing, setRefreshing] = useState(false);
  const [isUrdu, setIsUrdu] = useState(false);
  const promoScrollRef = useRef(null);
  
  const onRefresh = async () => {
    setRefreshing(true);
    // Simulate API refresh
    await new Promise(resolve => setTimeout(resolve, 1000));
    setRefreshing(false);
  };
  
  const handleLocationPress = () => {
    // Open location selector modal
    console.log('Open location selector');
  };
  
  const handleCategoryPress = (category) => {
    navigation.navigate('Search', { category: category.id });
  };
  
  const handleRestaurantPress = (restaurant) => {
    navigation.navigate('Restaurant', { restaurantId: restaurant.id });
  };
  
  const renderPromoItem = ({ item }) => (
    <Card
      style={[styles.promoCard, { backgroundColor: item.backgroundColor }]}
      onPress={() => console.log('Promo pressed:', item.id)}
    >
      <View style={styles.promoContent}>
        <Text style={styles.promoEmoji}>{item.image}</Text>
        <View style={styles.promoText}>
          <Text style={styles.promoTitle}>{item.title}</Text>
          <Text style={styles.promoSubtitle}>{item.subtitle}</Text>
        </View>
      </View>
    </Card>
  );
  
  const renderCategoryItem = ({ item }) => (
    <TouchableOpacity
      style={styles.categoryItem}
      onPress={() => handleCategoryPress(item)}
    >
      <View style={[styles.categoryIcon, { backgroundColor: item.color + '20' }]}>
        <Text style={styles.categoryEmoji}>{item.icon}</Text>
      </View>
      <Text style={styles.categoryName}>
        {isUrdu ? item.nameUrdu : item.name}
      </Text>
    </TouchableOpacity>
  );
  
  const renderRestaurantItem = ({ item }) => (
    <Card
      style={styles.restaurantCard}
      onPress={() => handleRestaurantPress(item)}
    >
      <View style={styles.restaurantImage}>
        <Text style={styles.restaurantEmoji}>{item.image}</Text>
        {item.featured && (
          <View style={styles.featuredBadge}>
            <Text style={styles.featuredText}>Featured</Text>
          </View>
        )}
      </View>
      
      <View style={styles.restaurantInfo}>
        <Text style={styles.restaurantName}>
          {isUrdu ? item.nameUrdu : item.name}
        </Text>
        <Text style={styles.restaurantCuisine}>{item.cuisine}</Text>
        
        <View style={styles.restaurantMeta}>
          <View style={styles.ratingContainer}>
            <Text style={styles.ratingText}>⭐ {item.rating}</Text>
          </View>
          <Text style={styles.deliveryTime}>{item.deliveryTime}</Text>
          <Text style={styles.deliveryFee}>
            {item.deliveryFee === 0 ? 'Free' : `PKR ${item.deliveryFee}`}
          </Text>
        </View>
      </View>
    </Card>
  );
  
  return (
    <View style={styles.container}>
      <StatusBar
        barStyle="dark-content"
        backgroundColor={Colors.background.primary}
      />
      
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.locationButton}
            onPress={handleLocationPress}
          >
            <Text style={styles.locationIcon}>📍</Text>
            <View style={styles.locationText}>
              <Text style={styles.locationLabel}>Deliver to</Text>
              <Text style={styles.locationValue}>
                {isUrdu ? selectedCity.nameUrdu : selectedCity.name}
              </Text>
            </View>
            <Text style={styles.locationArrow}>▼</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={styles.languageToggle}
            onPress={() => setIsUrdu(!isUrdu)}
          >
            <Text style={styles.languageToggleText}>
              {isUrdu ? 'EN' : 'اردو'}
            </Text>
          </TouchableOpacity>
        </View>
        
        {/* Promo Banner */}
        <View style={styles.section}>
          <FlatList
            ref={promoScrollRef}
            data={mockPromos}
            renderItem={renderPromoItem}
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.promoList}
            snapToInterval={280}
            decelerationRate="fast"
          />
        </View>
        
        {/* Food Categories */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>
            {isUrdu ? 'کھانے کی اقسام' : 'Food Categories'}
          </Text>
          <FlatList
            data={AppConfig.foodCategories}
            renderItem={renderCategoryItem}
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.categoryList}
          />
        </View>
        
        {/* Featured Restaurants */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>
              {isUrdu ? 'نمایاں ریستوراں' : 'Featured Restaurants'}
            </Text>
            <TouchableOpacity onPress={() => navigation.navigate('Search')}>
              <Text style={styles.seeAllText}>
                {isUrdu ? 'سب دیکھیں' : 'See All'}
              </Text>
            </TouchableOpacity>
          </View>
          
          <FlatList
            data={mockRestaurants.filter(r => r.featured)}
            renderItem={renderRestaurantItem}
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.restaurantList}
          />
        </View>
        
        {/* All Restaurants */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>
            {isUrdu ? 'تمام ریستوراں' : 'All Restaurants'}
          </Text>
          
          {mockRestaurants.map((restaurant) => (
            <View key={restaurant.id} style={styles.restaurantListItem}>
              {renderRestaurantItem({ item: restaurant })}
            </View>
          ))}
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background.primary,
  },

  scrollView: {
    flex: 1,
  },

  scrollContent: {
    paddingBottom: Spacing.xl,
  },

  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: Spacing.lg,
    paddingTop: Spacing.lg,
    paddingBottom: Spacing.md,
  },

  locationButton: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },

  locationIcon: {
    fontSize: 20,
    marginRight: Spacing.sm,
  },

  locationText: {
    flex: 1,
  },

  locationLabel: {
    ...Typography.styles.caption,
    color: Colors.text.tertiary,
  },

  locationValue: {
    ...Typography.styles.label,
    color: Colors.text.primary,
  },

  locationArrow: {
    ...Typography.styles.caption,
    color: Colors.text.tertiary,
    marginLeft: Spacing.xs,
  },

  languageToggle: {
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs,
    borderRadius: Spacing.radius.sm,
    backgroundColor: Colors.background.secondary,
  },

  languageToggleText: {
    ...Typography.styles.labelSmall,
    color: Colors.text.primary,
    fontWeight: '600',
  },

  section: {
    marginBottom: Spacing.lg,
  },

  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: Spacing.lg,
    marginBottom: Spacing.md,
  },

  sectionTitle: {
    ...Typography.styles.h5,
    color: Colors.text.primary,
    paddingHorizontal: Spacing.lg,
    marginBottom: Spacing.md,
  },

  seeAllText: {
    ...Typography.styles.label,
    color: Colors.primary.main,
  },

  // Promo styles
  promoList: {
    paddingHorizontal: Spacing.lg,
    gap: Spacing.md,
  },

  promoCard: {
    width: 280,
    height: 120,
    marginRight: Spacing.md,
  },

  promoContent: {
    flexDirection: 'row',
    alignItems: 'center',
    height: '100%',
    paddingHorizontal: Spacing.lg,
  },

  promoEmoji: {
    fontSize: 40,
    marginRight: Spacing.md,
  },

  promoText: {
    flex: 1,
  },

  promoTitle: {
    ...Typography.styles.h6,
    color: Colors.neutral.white,
    marginBottom: Spacing.xs,
  },

  promoSubtitle: {
    ...Typography.styles.bodySmall,
    color: Colors.neutral.white,
    opacity: 0.9,
  },

  // Category styles
  categoryList: {
    paddingHorizontal: Spacing.lg,
    gap: Spacing.md,
  },

  categoryItem: {
    alignItems: 'center',
    width: 80,
  },

  categoryIcon: {
    width: 60,
    height: 60,
    borderRadius: 30,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: Spacing.xs,
  },

  categoryEmoji: {
    fontSize: 24,
  },

  categoryName: {
    ...Typography.styles.caption,
    color: Colors.text.primary,
    textAlign: 'center',
  },

  // Restaurant styles
  restaurantList: {
    paddingHorizontal: Spacing.lg,
    gap: Spacing.md,
  },

  restaurantListItem: {
    marginHorizontal: Spacing.lg,
    marginBottom: Spacing.md,
  },

  restaurantCard: {
    width: 280,
    marginRight: Spacing.md,
  },

  restaurantImage: {
    height: 120,
    backgroundColor: Colors.background.secondary,
    borderRadius: Spacing.radius.md,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: Spacing.sm,
    position: 'relative',
  },

  restaurantEmoji: {
    fontSize: 40,
  },

  featuredBadge: {
    position: 'absolute',
    top: Spacing.sm,
    right: Spacing.sm,
    backgroundColor: Colors.primary.main,
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs,
    borderRadius: Spacing.radius.sm,
  },

  featuredText: {
    ...Typography.styles.caption,
    color: Colors.neutral.white,
    fontWeight: '600',
  },

  restaurantInfo: {
    paddingHorizontal: Spacing.sm,
  },

  restaurantName: {
    ...Typography.styles.label,
    color: Colors.text.primary,
    marginBottom: Spacing.xs,
  },

  restaurantCuisine: {
    ...Typography.styles.caption,
    color: Colors.text.secondary,
    marginBottom: Spacing.sm,
  },

  restaurantMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.sm,
  },

  ratingContainer: {
    backgroundColor: Colors.success.bg,
    paddingHorizontal: Spacing.xs,
    paddingVertical: 2,
    borderRadius: Spacing.radius.sm,
  },

  ratingText: {
    ...Typography.styles.caption,
    color: Colors.success.main,
    fontWeight: '600',
  },

  deliveryTime: {
    ...Typography.styles.caption,
    color: Colors.text.tertiary,
  },

  deliveryFee: {
    ...Typography.styles.caption,
    color: Colors.text.tertiary,
  },
});

export default HomeScreen;
