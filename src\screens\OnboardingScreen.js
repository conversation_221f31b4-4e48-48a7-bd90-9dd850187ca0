import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Dimensions,
  FlatList,
  TouchableOpacity,
  StatusBar,
} from 'react-native';
import { Colors, Typography, Spacing } from '../constants';
import Button from '../components/common/Button';

const { width, height } = Dimensions.get('window');

const onboardingData = [
  {
    id: '1',
    title: 'Fast Food Delivery',
    titleUrdu: 'تیز فوڈ ڈیلیوری',
    description: 'Get your favorite food delivered to your doorstep in minutes',
    descriptionUrdu: 'اپنا پسندیدہ کھانا منٹوں میں اپنے گھر تک پہنچائیں',
    emoji: '🚀',
    backgroundColor: Colors.primary.main,
  },
  {
    id: '2',
    title: 'Local Flavors',
    titleUrdu: 'مقامی ذائقے',
    description: 'Discover authentic Pakistani cuisine from local restaurants',
    descriptionUrdu: 'مقامی ریستورانوں سے اصل پاکستانی کھانے کا مزہ لیں',
    emoji: '🍛',
    backgroundColor: Colors.secondary.main,
  },
  {
    id: '3',
    title: 'Secure Payments',
    titleUrdu: 'محفوظ ادائیگی',
    description: 'Pay safely with JazzCash, Easypaisa, or Cash on Delivery',
    descriptionUrdu: 'جاز کیش، ایزی پیسہ یا کیش آن ڈیلیوری سے محفوظ ادائیگی',
    emoji: '💳',
    backgroundColor: Colors.accent.main,
  },
];

const OnboardingScreen = ({ navigation }) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isUrdu, setIsUrdu] = useState(false);
  const flatListRef = useRef(null);
  
  const handleNext = () => {
    if (currentIndex < onboardingData.length - 1) {
      const nextIndex = currentIndex + 1;
      setCurrentIndex(nextIndex);
      flatListRef.current?.scrollToIndex({ index: nextIndex, animated: true });
    } else {
      handleGetStarted();
    }
  };
  
  const handlePrevious = () => {
    if (currentIndex > 0) {
      const prevIndex = currentIndex - 1;
      setCurrentIndex(prevIndex);
      flatListRef.current?.scrollToIndex({ index: prevIndex, animated: true });
    }
  };
  
  const handleGetStarted = () => {
    navigation.replace('MainTabs');
  };
  
  const handleSkip = () => {
    navigation.replace('MainTabs');
  };
  
  const toggleLanguage = () => {
    setIsUrdu(!isUrdu);
  };
  
  const renderOnboardingItem = ({ item }) => (
    <View style={[styles.slide, { backgroundColor: item.backgroundColor }]}>
      <View style={styles.content}>
        {/* Emoji Icon */}
        <View style={styles.iconContainer}>
          <Text style={styles.emoji}>{item.emoji}</Text>
        </View>
        
        {/* Title */}
        <Text style={[styles.title, isUrdu && styles.titleUrdu]}>
          {isUrdu ? item.titleUrdu : item.title}
        </Text>
        
        {/* Description */}
        <Text style={[styles.description, isUrdu && styles.descriptionUrdu]}>
          {isUrdu ? item.descriptionUrdu : item.description}
        </Text>
      </View>
    </View>
  );
  
  const renderPagination = () => (
    <View style={styles.pagination}>
      {onboardingData.map((_, index) => (
        <View
          key={index}
          style={[
            styles.paginationDot,
            index === currentIndex && styles.paginationDotActive,
          ]}
        />
      ))}
    </View>
  );
  
  return (
    <View style={styles.container}>
      <StatusBar
        barStyle="light-content"
        backgroundColor={onboardingData[currentIndex].backgroundColor}
        translucent={false}
      />
      
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={toggleLanguage} style={styles.languageButton}>
          <Text style={styles.languageButtonText}>
            {isUrdu ? 'English' : 'اردو'}
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity onPress={handleSkip} style={styles.skipButton}>
          <Text style={styles.skipButtonText}>
            {isUrdu ? 'چھوڑیں' : 'Skip'}
          </Text>
        </TouchableOpacity>
      </View>
      
      {/* Slides */}
      <FlatList
        ref={flatListRef}
        data={onboardingData}
        renderItem={renderOnboardingItem}
        horizontal
        pagingEnabled
        showsHorizontalScrollIndicator={false}
        keyExtractor={(item) => item.id}
        onMomentumScrollEnd={(event) => {
          const index = Math.round(event.nativeEvent.contentOffset.x / width);
          setCurrentIndex(index);
        }}
        scrollEnabled={false} // Disable manual scrolling, use buttons only
      />
      
      {/* Bottom Section */}
      <View style={styles.bottomSection}>
        {renderPagination()}
        
        <View style={styles.buttonContainer}>
          {currentIndex > 0 && (
            <Button
              title={isUrdu ? 'پچھلا' : 'Previous'}
              onPress={handlePrevious}
              variant="ghost"
              style={styles.previousButton}
              textStyle={styles.buttonText}
            />
          )}
          
          <Button
            title={
              currentIndex === onboardingData.length - 1
                ? isUrdu ? 'شروع کریں' : 'Get Started'
                : isUrdu ? 'اگلا' : 'Next'
            }
            onPress={handleNext}
            variant="secondary"
            style={styles.nextButton}
            textStyle={styles.buttonText}
          />
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.primary.main,
  },
  
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: Spacing.lg,
    paddingTop: Spacing.md,
    paddingBottom: Spacing.sm,
  },
  
  languageButton: {
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
    borderRadius: Spacing.radius.md,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
  
  languageButtonText: {
    ...Typography.styles.labelSmall,
    color: Colors.neutral.white,
    fontWeight: '600',
  },
  
  skipButton: {
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
  },
  
  skipButtonText: {
    ...Typography.styles.body,
    color: Colors.neutral.white,
    opacity: 0.8,
  },
  
  slide: {
    width,
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  
  content: {
    alignItems: 'center',
    paddingHorizontal: Spacing.xl,
    maxWidth: width * 0.8,
  },
  
  iconContainer: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: Spacing.xl,
  },
  
  emoji: {
    fontSize: 60,
  },
  
  title: {
    ...Typography.styles.h2,
    color: Colors.neutral.white,
    textAlign: 'center',
    marginBottom: Spacing.lg,
    fontWeight: '700',
  },
  
  titleUrdu: {
    textAlign: 'right',
    lineHeight: Typography.styles.h2.lineHeight * 1.2,
  },
  
  description: {
    ...Typography.styles.bodyLarge,
    color: Colors.neutral.white,
    textAlign: 'center',
    opacity: 0.9,
    lineHeight: 24,
  },
  
  descriptionUrdu: {
    textAlign: 'right',
    lineHeight: 28,
  },
  
  bottomSection: {
    paddingHorizontal: Spacing.lg,
    paddingBottom: Spacing.xl,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
  },
  
  pagination: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: Spacing.xl,
    gap: Spacing.sm,
  },
  
  paginationDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: 'rgba(255, 255, 255, 0.4)',
  },
  
  paginationDotActive: {
    backgroundColor: Colors.neutral.white,
    width: 24,
  },
  
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    gap: Spacing.md,
  },
  
  previousButton: {
    flex: 1,
    backgroundColor: 'transparent',
  },
  
  nextButton: {
    flex: 2,
    backgroundColor: Colors.neutral.white,
  },
  
  buttonText: {
    fontWeight: '600',
  },
});

export default OnboardingScreen;
