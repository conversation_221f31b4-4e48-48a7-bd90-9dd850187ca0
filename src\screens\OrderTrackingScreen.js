import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  StatusBar,
} from 'react-native';
import { Colors, Typography, Spacing, AppConfig } from '../constants';
import Card from '../components/common/Card';
import Button from '../components/common/Button';

const OrderTrackingScreen = ({ navigation, route }) => {
  const { orderId, total } = route.params || {};
  const [currentStatus, setCurrentStatus] = useState(0);
  const [estimatedTime, setEstimatedTime] = useState(25);
  
  // Simulate order progress
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentStatus(prev => {
        if (prev < AppConfig.orderStatus.length - 2) { // Don't auto-complete to delivered
          setEstimatedTime(prevTime => Math.max(0, prevTime - 1));
          return prev + 1;
        }
        return prev;
      });
    }, 10000); // Progress every 10 seconds for demo
    
    return () => clearInterval(interval);
  }, []);
  
  const getStatusIcon = (index) => {
    if (index < currentStatus) return '✅';
    if (index === currentStatus) return '🔄';
    return '⭕';
  };
  
  const getStatusColor = (index) => {
    if (index < currentStatus) return Colors.success.main;
    if (index === currentStatus) return Colors.primary.main;
    return Colors.text.tertiary;
  };
  
  return (
    <View style={styles.container}>
      <StatusBar
        barStyle="dark-content"
        backgroundColor={Colors.background.primary}
      />
      
      <ScrollView style={styles.content}>
        {/* Order Header */}
        <Card style={styles.orderHeader}>
          <Text style={styles.orderId}>Order #{orderId}</Text>
          <Text style={styles.orderTotal}>Total: PKR {total}</Text>
          
          {currentStatus < AppConfig.orderStatus.length - 1 && (
            <View style={styles.estimatedTime}>
              <Text style={styles.estimatedTimeLabel}>Estimated Time</Text>
              <Text style={styles.estimatedTimeValue}>{estimatedTime} minutes</Text>
            </View>
          )}
        </Card>
        
        {/* Order Progress */}
        <Card style={styles.progressCard}>
          <Text style={styles.progressTitle}>Order Status</Text>
          
          <View style={styles.progressContainer}>
            {AppConfig.orderStatus.slice(0, -1).map((status, index) => (
              <View key={status.id} style={styles.progressStep}>
                <View style={styles.progressStepLeft}>
                  <View
                    style={[
                      styles.progressIcon,
                      { backgroundColor: getStatusColor(index) + '20' },
                    ]}
                  >
                    <Text style={styles.progressEmoji}>
                      {getStatusIcon(index)}
                    </Text>
                  </View>
                  
                  {index < AppConfig.orderStatus.length - 2 && (
                    <View
                      style={[
                        styles.progressLine,
                        {
                          backgroundColor: index < currentStatus
                            ? Colors.success.main
                            : Colors.border.light,
                        },
                      ]}
                    />
                  )}
                </View>
                
                <View style={styles.progressStepRight}>
                  <Text
                    style={[
                      styles.progressStepTitle,
                      { color: getStatusColor(index) },
                    ]}
                  >
                    {status.name}
                  </Text>
                  
                  {index === currentStatus && (
                    <Text style={styles.progressStepSubtitle}>
                      In progress...
                    </Text>
                  )}
                  
                  {index < currentStatus && (
                    <Text style={styles.progressStepSubtitle}>
                      Completed
                    </Text>
                  )}
                </View>
              </View>
            ))}
          </View>
        </Card>
        
        {/* Rider Info (when order is on the way) */}
        {currentStatus >= 2 && (
          <Card style={styles.riderCard}>
            <Text style={styles.riderTitle}>Your Delivery Rider</Text>
            
            <View style={styles.riderInfo}>
              <View style={styles.riderAvatar}>
                <Text style={styles.riderEmoji}>🏍️</Text>
              </View>
              
              <View style={styles.riderDetails}>
                <Text style={styles.riderName}>Ahmed Khan</Text>
                <Text style={styles.riderPhone}>+92 300 1234567</Text>
                <View style={styles.riderRating}>
                  <Text style={styles.ratingText}>⭐ 4.8</Text>
                </View>
              </View>
            </View>
            
            <View style={styles.riderActions}>
              <Button
                title="Call Rider"
                variant="outline"
                size="small"
                style={styles.riderButton}
              />
              <Button
                title="Track Live"
                size="small"
                style={styles.riderButton}
              />
            </View>
          </Card>
        )}
        
        {/* Support */}
        <Card style={styles.supportCard}>
          <Text style={styles.supportTitle}>Need Help?</Text>
          <Text style={styles.supportText}>
            Contact our support team for any issues with your order
          </Text>
          
          <View style={styles.supportActions}>
            <Button
              title="Contact Support"
              variant="outline"
              fullWidth
            />
          </View>
        </Card>
      </ScrollView>
      
      {/* Footer */}
      {currentStatus >= AppConfig.orderStatus.length - 2 && (
        <View style={styles.footer}>
          <Button
            title="Order Again"
            onPress={() => navigation.navigate('Home')}
            fullWidth
            size="large"
          />
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background.primary,
  },
  
  content: {
    flex: 1,
    paddingHorizontal: Spacing.lg,
    paddingTop: Spacing.md,
  },
  
  orderHeader: {
    padding: Spacing.lg,
    alignItems: 'center',
    marginBottom: Spacing.lg,
  },
  
  orderId: {
    ...Typography.styles.h5,
    color: Colors.text.primary,
    marginBottom: Spacing.xs,
  },
  
  orderTotal: {
    ...Typography.styles.body,
    color: Colors.text.secondary,
    marginBottom: Spacing.md,
  },
  
  estimatedTime: {
    alignItems: 'center',
  },
  
  estimatedTimeLabel: {
    ...Typography.styles.caption,
    color: Colors.text.tertiary,
  },
  
  estimatedTimeValue: {
    ...Typography.styles.h4,
    color: Colors.primary.main,
    fontWeight: '700',
  },
  
  progressCard: {
    padding: Spacing.lg,
    marginBottom: Spacing.lg,
  },
  
  progressTitle: {
    ...Typography.styles.h6,
    color: Colors.text.primary,
    marginBottom: Spacing.lg,
  },
  
  progressContainer: {
    paddingLeft: Spacing.sm,
  },
  
  progressStep: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  
  progressStepLeft: {
    alignItems: 'center',
    marginRight: Spacing.md,
  },
  
  progressIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: Spacing.xs,
  },
  
  progressEmoji: {
    fontSize: 16,
  },
  
  progressLine: {
    width: 2,
    height: 40,
    marginBottom: Spacing.xs,
  },
  
  progressStepRight: {
    flex: 1,
    paddingBottom: Spacing.lg,
  },
  
  progressStepTitle: {
    ...Typography.styles.label,
    marginBottom: Spacing.xs,
  },
  
  progressStepSubtitle: {
    ...Typography.styles.caption,
    color: Colors.text.tertiary,
  },
  
  riderCard: {
    padding: Spacing.lg,
    marginBottom: Spacing.lg,
  },
  
  riderTitle: {
    ...Typography.styles.h6,
    color: Colors.text.primary,
    marginBottom: Spacing.md,
  },
  
  riderInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.md,
  },
  
  riderAvatar: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: Colors.primary[50],
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: Spacing.md,
  },
  
  riderEmoji: {
    fontSize: 24,
  },
  
  riderDetails: {
    flex: 1,
  },
  
  riderName: {
    ...Typography.styles.label,
    color: Colors.text.primary,
    marginBottom: Spacing.xs,
  },
  
  riderPhone: {
    ...Typography.styles.bodySmall,
    color: Colors.text.secondary,
    marginBottom: Spacing.xs,
  },
  
  riderRating: {
    alignSelf: 'flex-start',
  },
  
  ratingText: {
    ...Typography.styles.caption,
    color: Colors.success.main,
    backgroundColor: Colors.success.bg,
    paddingHorizontal: Spacing.xs,
    paddingVertical: 2,
    borderRadius: Spacing.radius.sm,
  },
  
  riderActions: {
    flexDirection: 'row',
    gap: Spacing.md,
  },
  
  riderButton: {
    flex: 1,
  },
  
  supportCard: {
    padding: Spacing.lg,
    marginBottom: Spacing.lg,
  },
  
  supportTitle: {
    ...Typography.styles.h6,
    color: Colors.text.primary,
    marginBottom: Spacing.sm,
  },
  
  supportText: {
    ...Typography.styles.body,
    color: Colors.text.secondary,
    marginBottom: Spacing.md,
  },
  
  supportActions: {
    // Actions container
  },
  
  footer: {
    padding: Spacing.lg,
    backgroundColor: Colors.background.primary,
    borderTopWidth: 1,
    borderTopColor: Colors.border.light,
  },
});

export default OrderTrackingScreen;
