import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  StatusBar,
  Switch,
} from 'react-native';
import { Colors, Typography, Spacing, AppConfig } from '../constants';
import Card from '../components/common/Card';
import Button from '../components/common/Button';

const ProfileScreen = ({ navigation }) => {
  const [isUrdu, setIsUrdu] = useState(false);
  const [darkMode, setDarkMode] = useState(false);
  const [notifications, setNotifications] = useState(true);
  const [otpEnabled, setOtpEnabled] = useState(AppConfig.features.otpVerification);
  
  const profileOptions = [
    {
      id: 'orders',
      title: 'Order History',
      titleUrdu: 'آرڈر کی تاریخ',
      icon: '📋',
      onPress: () => console.log('Order History'),
    },
    {
      id: 'addresses',
      title: 'Saved Addresses',
      titleUrdu: 'محفوظ پتے',
      icon: '📍',
      onPress: () => console.log('Saved Addresses'),
    },
    {
      id: 'payment',
      title: 'Payment Methods',
      titleUrdu: 'ادائیگی کے طریقے',
      icon: '💳',
      onPress: () => console.log('Payment Methods'),
    },
    {
      id: 'favorites',
      title: 'Favorite Restaurants',
      titleUrdu: 'پسندیدہ ریستوراں',
      icon: '❤️',
      onPress: () => console.log('Favorites'),
    },
  ];
  
  const settingsOptions = [
    {
      id: 'language',
      title: 'Language',
      titleUrdu: 'زبان',
      icon: '🌐',
      value: isUrdu ? 'Urdu' : 'English',
      onPress: () => setIsUrdu(!isUrdu),
    },
    {
      id: 'notifications',
      title: 'Notifications',
      titleUrdu: 'اطلاعات',
      icon: '🔔',
      hasSwitch: true,
      value: notifications,
      onPress: () => setNotifications(!notifications),
    },
    {
      id: 'darkMode',
      title: 'Dark Mode',
      titleUrdu: 'ڈارک موڈ',
      icon: '🌙',
      hasSwitch: true,
      value: darkMode,
      onPress: () => setDarkMode(!darkMode),
    },
  ];
  
  const securityOptions = [
    {
      id: 'otp',
      title: 'OTP Verification',
      titleUrdu: 'او ٹی پی تصدیق',
      icon: '🔐',
      subtitle: 'Enable for enhanced security',
      subtitleUrdu: 'بہتر سیکیورٹی کے لیے فعال کریں',
      hasSwitch: true,
      value: otpEnabled,
      onPress: () => setOtpEnabled(!otpEnabled),
    },
  ];
  
  const renderOption = (option) => (
    <TouchableOpacity
      key={option.id}
      style={styles.optionItem}
      onPress={option.onPress}
    >
      <View style={styles.optionLeft}>
        <Text style={styles.optionIcon}>{option.icon}</Text>
        <View style={styles.optionText}>
          <Text style={styles.optionTitle}>
            {isUrdu ? option.titleUrdu : option.title}
          </Text>
          {option.subtitle && (
            <Text style={styles.optionSubtitle}>
              {isUrdu ? option.subtitleUrdu : option.subtitle}
            </Text>
          )}
        </View>
      </View>
      
      <View style={styles.optionRight}>
        {option.hasSwitch ? (
          <Switch
            value={option.value}
            onValueChange={option.onPress}
            trackColor={{
              false: Colors.neutral[300],
              true: Colors.primary.light,
            }}
            thumbColor={option.value ? Colors.primary.main : Colors.neutral.white}
          />
        ) : option.value ? (
          <Text style={styles.optionValue}>{option.value}</Text>
        ) : (
          <Text style={styles.optionArrow}>›</Text>
        )}
      </View>
    </TouchableOpacity>
  );
  
  return (
    <View style={styles.container}>
      <StatusBar
        barStyle="dark-content"
        backgroundColor={Colors.background.primary}
      />
      
      <ScrollView style={styles.content}>
        {/* Profile Header */}
        <Card style={styles.profileHeader}>
          <View style={styles.avatarContainer}>
            <View style={styles.avatar}>
              <Text style={styles.avatarText}>👤</Text>
            </View>
            <TouchableOpacity style={styles.editAvatarButton}>
              <Text style={styles.editAvatarText}>📷</Text>
            </TouchableOpacity>
          </View>
          
          <Text style={styles.userName}>John Doe</Text>
          <Text style={styles.userPhone}>+92 300 1234567</Text>
          
          <Button
            title={isUrdu ? 'پروفائل ایڈٹ کریں' : 'Edit Profile'}
            variant="outline"
            size="small"
            style={styles.editProfileButton}
          />
        </Card>
        
        {/* Profile Options */}
        <Card style={styles.section}>
          <Text style={styles.sectionTitle}>
            {isUrdu ? 'اکاؤنٹ' : 'Account'}
          </Text>
          {profileOptions.map(renderOption)}
        </Card>
        
        {/* Settings */}
        <Card style={styles.section}>
          <Text style={styles.sectionTitle}>
            {isUrdu ? 'ترتیبات' : 'Settings'}
          </Text>
          {settingsOptions.map(renderOption)}
        </Card>
        
        {/* Security */}
        <Card style={styles.section}>
          <Text style={styles.sectionTitle}>
            {isUrdu ? 'سیکیورٹی' : 'Security'}
          </Text>
          {securityOptions.map(renderOption)}
        </Card>
        
        {/* Support */}
        <Card style={styles.section}>
          <Text style={styles.sectionTitle}>
            {isUrdu ? 'مدد' : 'Support'}
          </Text>
          
          <TouchableOpacity style={styles.optionItem}>
            <View style={styles.optionLeft}>
              <Text style={styles.optionIcon}>❓</Text>
              <Text style={styles.optionTitle}>
                {isUrdu ? 'مدد اور سپورٹ' : 'Help & Support'}
              </Text>
            </View>
            <Text style={styles.optionArrow}>›</Text>
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.optionItem}>
            <View style={styles.optionLeft}>
              <Text style={styles.optionIcon}>📄</Text>
              <Text style={styles.optionTitle}>
                {isUrdu ? 'شرائط و ضوابط' : 'Terms & Conditions'}
              </Text>
            </View>
            <Text style={styles.optionArrow}>›</Text>
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.optionItem}>
            <View style={styles.optionLeft}>
              <Text style={styles.optionIcon}>🔒</Text>
              <Text style={styles.optionTitle}>
                {isUrdu ? 'پرائیویسی پالیسی' : 'Privacy Policy'}
              </Text>
            </View>
            <Text style={styles.optionArrow}>›</Text>
          </TouchableOpacity>
        </Card>
        
        {/* Logout */}
        <Card style={styles.section}>
          <TouchableOpacity style={[styles.optionItem, styles.logoutOption]}>
            <View style={styles.optionLeft}>
              <Text style={styles.optionIcon}>🚪</Text>
              <Text style={[styles.optionTitle, styles.logoutText]}>
                {isUrdu ? 'لاگ آؤٹ' : 'Logout'}
              </Text>
            </View>
          </TouchableOpacity>
        </Card>
        
        {/* App Version */}
        <View style={styles.versionContainer}>
          <Text style={styles.versionText}>
            {AppConfig.name} v{AppConfig.version}
          </Text>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background.primary,
  },
  
  content: {
    flex: 1,
    paddingHorizontal: Spacing.lg,
    paddingTop: Spacing.lg,
  },
  
  profileHeader: {
    padding: Spacing.lg,
    alignItems: 'center',
    marginBottom: Spacing.lg,
  },
  
  avatarContainer: {
    position: 'relative',
    marginBottom: Spacing.md,
  },
  
  avatar: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: Colors.primary[50],
    alignItems: 'center',
    justifyContent: 'center',
  },
  
  avatarText: {
    fontSize: 32,
  },
  
  editAvatarButton: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    width: 28,
    height: 28,
    borderRadius: 14,
    backgroundColor: Colors.primary.main,
    alignItems: 'center',
    justifyContent: 'center',
  },
  
  editAvatarText: {
    fontSize: 12,
  },
  
  userName: {
    ...Typography.styles.h5,
    color: Colors.text.primary,
    marginBottom: Spacing.xs,
  },
  
  userPhone: {
    ...Typography.styles.body,
    color: Colors.text.secondary,
    marginBottom: Spacing.md,
  },
  
  editProfileButton: {
    paddingHorizontal: Spacing.lg,
  },
  
  section: {
    padding: Spacing.lg,
    marginBottom: Spacing.lg,
  },
  
  sectionTitle: {
    ...Typography.styles.h6,
    color: Colors.text.primary,
    marginBottom: Spacing.md,
  },
  
  optionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: Spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border.light,
  },
  
  optionLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  
  optionIcon: {
    fontSize: 20,
    marginRight: Spacing.md,
    width: 24,
    textAlign: 'center',
  },
  
  optionText: {
    flex: 1,
  },
  
  optionTitle: {
    ...Typography.styles.body,
    color: Colors.text.primary,
  },
  
  optionSubtitle: {
    ...Typography.styles.caption,
    color: Colors.text.secondary,
    marginTop: Spacing.xs,
  },
  
  optionRight: {
    alignItems: 'center',
  },
  
  optionValue: {
    ...Typography.styles.body,
    color: Colors.text.secondary,
  },
  
  optionArrow: {
    ...Typography.styles.h5,
    color: Colors.text.tertiary,
    fontWeight: '300',
  },
  
  logoutOption: {
    borderBottomWidth: 0,
  },
  
  logoutText: {
    color: Colors.error.main,
  },
  
  versionContainer: {
    alignItems: 'center',
    paddingVertical: Spacing.lg,
  },
  
  versionText: {
    ...Typography.styles.caption,
    color: Colors.text.tertiary,
  },
});

export default ProfileScreen;
