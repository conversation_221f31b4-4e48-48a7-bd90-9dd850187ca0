import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  StatusBar,
} from 'react-native';
import { Colors, Typography, Spacing } from '../constants';
import Card from '../components/common/Card';
import Button from '../components/common/Button';

const RestaurantScreen = ({ navigation, route }) => {
  const [selectedTab, setSelectedTab] = useState('starters');
  const [isUrdu, setIsUrdu] = useState(false);
  
  const tabs = [
    { id: 'starters', name: 'Starters', nameUrdu: 'اسٹارٹرز' },
    { id: 'mains', name: 'Main Course', nameUrdu: 'مین کورس' },
    { id: 'deals', name: 'Deals', nameUrdu: 'ڈیلز' },
    { id: 'drinks', name: 'Drinks', nameUrdu: 'مشروبات' },
  ];
  
  return (
    <View style={styles.container}>
      <StatusBar
        barStyle="dark-content"
        backgroundColor={Colors.background.primary}
      />
      
      <ScrollView style={styles.content}>
        {/* Restaurant Header */}
        <Card style={styles.restaurantHeader}>
          <View style={styles.restaurantImage}>
            <Text style={styles.restaurantEmoji}>🍛</Text>
          </View>
          
          <View style={styles.restaurantInfo}>
            <Text style={styles.restaurantName}>Karachi Biryani House</Text>
            <Text style={styles.restaurantCuisine}>Pakistani • Biryani</Text>
            
            <View style={styles.restaurantMeta}>
              <Text style={styles.rating}>⭐ 4.5</Text>
              <Text style={styles.deliveryTime}>25-35 min</Text>
              <Text style={styles.deliveryFee}>PKR 49 delivery</Text>
            </View>
          </View>
        </Card>
        
        {/* Menu Tabs */}
        <View style={styles.tabContainer}>
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            {tabs.map((tab) => (
              <TouchableOpacity
                key={tab.id}
                style={[
                  styles.tab,
                  selectedTab === tab.id && styles.tabActive,
                ]}
                onPress={() => setSelectedTab(tab.id)}
              >
                <Text
                  style={[
                    styles.tabText,
                    selectedTab === tab.id && styles.tabTextActive,
                  ]}
                >
                  {isUrdu ? tab.nameUrdu : tab.name}
                </Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>
        
        {/* Menu Items Placeholder */}
        <Card style={styles.placeholderCard}>
          <Text style={styles.placeholderTitle}>Restaurant Menu</Text>
          <Text style={styles.placeholderText}>
            This screen will contain:
            {'\n'}• Restaurant details and info
            {'\n'}• Tabbed menu categories
            {'\n'}• Food items with images and prices
            {'\n'}• Add to cart functionality
            {'\n'}• Item customization options
          </Text>
          <Text style={styles.selectedTabText}>
            Selected Tab: {selectedTab}
          </Text>
        </Card>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background.primary,
  },
  
  content: {
    flex: 1,
  },
  
  restaurantHeader: {
    margin: Spacing.lg,
    padding: Spacing.lg,
  },
  
  restaurantImage: {
    height: 120,
    backgroundColor: Colors.background.secondary,
    borderRadius: Spacing.radius.md,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: Spacing.md,
  },
  
  restaurantEmoji: {
    fontSize: 48,
  },
  
  restaurantInfo: {
    alignItems: 'center',
  },
  
  restaurantName: {
    ...Typography.styles.h4,
    color: Colors.text.primary,
    marginBottom: Spacing.xs,
  },
  
  restaurantCuisine: {
    ...Typography.styles.body,
    color: Colors.text.secondary,
    marginBottom: Spacing.sm,
  },
  
  restaurantMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.md,
  },
  
  rating: {
    ...Typography.styles.labelSmall,
    color: Colors.success.main,
    backgroundColor: Colors.success.bg,
    paddingHorizontal: Spacing.xs,
    paddingVertical: 2,
    borderRadius: Spacing.radius.sm,
  },
  
  deliveryTime: {
    ...Typography.styles.caption,
    color: Colors.text.tertiary,
  },
  
  deliveryFee: {
    ...Typography.styles.caption,
    color: Colors.text.tertiary,
  },
  
  tabContainer: {
    borderBottomWidth: 1,
    borderBottomColor: Colors.border.light,
    paddingHorizontal: Spacing.lg,
  },
  
  tab: {
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
    marginRight: Spacing.md,
    borderBottomWidth: 2,
    borderBottomColor: 'transparent',
  },
  
  tabActive: {
    borderBottomColor: Colors.primary.main,
  },
  
  tabText: {
    ...Typography.styles.label,
    color: Colors.text.secondary,
  },
  
  tabTextActive: {
    color: Colors.primary.main,
    fontWeight: '600',
  },
  
  placeholderCard: {
    margin: Spacing.lg,
    padding: Spacing.lg,
    alignItems: 'center',
  },
  
  placeholderTitle: {
    ...Typography.styles.h4,
    color: Colors.text.primary,
    marginBottom: Spacing.md,
  },
  
  placeholderText: {
    ...Typography.styles.body,
    color: Colors.text.secondary,
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: Spacing.md,
  },
  
  selectedTabText: {
    ...Typography.styles.label,
    color: Colors.primary.main,
  },
});

export default RestaurantScreen;
