import { useState } from 'react';
import {
    ScrollView,
    StatusBar,
    StyleSheet,
    Text,
    TouchableOpacity,
    View
} from 'react-native';
import Card from '../components/common/Card';
import { Colors, Spacing, Typography } from '../constants';

// Mock menu data
const menuData = {
  starters: [
    {
      id: 'st1',
      name: 'Chicken Tikka',
      nameUrdu: 'چکن ٹکہ',
      description: 'Tender chicken pieces marinated in spices and grilled to perfection',
      descriptionUrdu: 'مسالوں میں میرینیٹ کیے گئے نرم چکن کے ٹکڑے',
      price: 450,
      image: '🍗',
      category: 'starters',
      isVeg: false,
      spiceLevel: 'medium',
      popular: true,
      customizations: [
        { id: 'spice', name: 'Spice Level', options: ['Mild', 'Medium', 'Hot'] },
      ],
    },
    {
      id: 'st2',
      name: 'Samosa',
      nameUrdu: 'سموسہ',
      description: 'Crispy pastry filled with spiced potatoes and peas',
      descriptionUrdu: 'مسالہ دار آلو اور مٹر سے بھرا کرسپی پیسٹری',
      price: 120,
      image: '🥟',
      category: 'starters',
      isVeg: true,
      spiceLevel: 'mild',
      popular: false,
    },
  ],
  mains: [
    {
      id: 'm1',
      name: 'Chicken Biryani',
      nameUrdu: 'چکن بریانی',
      description: 'Aromatic basmati rice cooked with tender chicken and traditional spices',
      descriptionUrdu: 'خوشبودار باسمتی چاول نرم چکن اور روایتی مسالوں کے ساتھ',
      price: 650,
      image: '🍛',
      category: 'mains',
      isVeg: false,
      spiceLevel: 'medium',
      popular: true,
      customizations: [
        { id: 'size', name: 'Size', options: ['Regular', 'Large'], prices: [0, 150] },
        { id: 'spice', name: 'Spice Level', options: ['Mild', 'Medium', 'Hot'] },
      ],
    },
    {
      id: 'm2',
      name: 'Mutton Karahi',
      nameUrdu: 'مٹن کڑاہی',
      description: 'Traditional mutton curry cooked in a wok with tomatoes and spices',
      descriptionUrdu: 'روایتی مٹن کری ٹماٹر اور مسالوں کے ساتھ کڑاہی میں پکایا گیا',
      price: 850,
      image: '🍖',
      category: 'mains',
      isVeg: false,
      spiceLevel: 'hot',
      popular: true,
    },
  ],
  deals: [
    {
      id: 'd1',
      name: 'Family Feast',
      nameUrdu: 'فیملی فیسٹ',
      description: 'Chicken Biryani + 2 Naan + Raita + Dessert (Serves 4)',
      descriptionUrdu: 'چکن بریانی + 2 نان + رائتہ + میٹھا (4 افراد کے لیے)',
      price: 1200,
      originalPrice: 1500,
      image: '🍽️',
      category: 'deals',
      isVeg: false,
      popular: true,
      discount: 20,
    },
  ],
  drinks: [
    {
      id: 'dr1',
      name: 'Fresh Lime Water',
      nameUrdu: 'تازہ لیمو پانی',
      description: 'Refreshing lime water with mint',
      descriptionUrdu: 'پودینے کے ساتھ تازگی بخش لیمو پانی',
      price: 80,
      image: '🍋',
      category: 'drinks',
      isVeg: true,
      customizations: [
        { id: 'sugar', name: 'Sugar Level', options: ['No Sugar', 'Less Sweet', 'Normal', 'Extra Sweet'] },
      ],
    },
  ],
};

const RestaurantScreen = ({ navigation, route }) => {
  const { restaurant } = route.params || {};
  const [selectedTab, setSelectedTab] = useState('starters');
  const [isUrdu, setIsUrdu] = useState(false);
  const [cart, setCart] = useState([]);
  const [selectedItem, setSelectedItem] = useState(null);
  const [showCustomization, setShowCustomization] = useState(false);
  const [customizations, setCustomizations] = useState({});
  const [quantity, setQuantity] = useState(1);

  const tabs = [
    { id: 'starters', name: 'Starters', nameUrdu: 'اسٹارٹرز' },
    { id: 'mains', name: 'Main Course', nameUrdu: 'مین کورس' },
    { id: 'deals', name: 'Deals', nameUrdu: 'ڈیلز' },
    { id: 'drinks', name: 'Drinks', nameUrdu: 'مشروبات' },
  ];

  // Cart functionality
  const addToCart = (item, customizations = {}, quantity = 1) => {
    const cartItem = {
      ...item,
      cartId: Date.now().toString(),
      quantity,
      customizations,
      totalPrice: calculateItemPrice(item, customizations, quantity),
    };

    setCart(prevCart => [...prevCart, cartItem]);
    Alert.alert('Added to Cart', `${item.name} has been added to your cart`);
  };

  const calculateItemPrice = (item, customizations, quantity) => {
    let basePrice = item.price;

    // Add customization prices
    Object.entries(customizations).forEach(([key, value]) => {
      const customization = item.customizations?.find(c => c.id === key);
      if (customization && customization.prices) {
        const optionIndex = customization.options.indexOf(value);
        if (optionIndex !== -1 && customization.prices[optionIndex]) {
          basePrice += customization.prices[optionIndex];
        }
      }
    });

    return basePrice * quantity;
  };

  const handleItemPress = (item) => {
    if (item.customizations && item.customizations.length > 0) {
      setSelectedItem(item);
      setCustomizations({});
      setQuantity(1);
      setShowCustomization(true);
    } else {
      addToCart(item);
    }
  };

  const handleAddToCartWithCustomizations = () => {
    if (selectedItem) {
      addToCart(selectedItem, customizations, quantity);
      setShowCustomization(false);
      setSelectedItem(null);
    }
  };

  const getCartItemCount = () => {
    return cart.reduce((total, item) => total + item.quantity, 0);
  };

  const getCartTotal = () => {
    return cart.reduce((total, item) => total + item.totalPrice, 0);
  };
  
  return (
    <View style={styles.container}>
      <StatusBar
        barStyle="dark-content"
        backgroundColor={Colors.background.primary}
      />
      
      <ScrollView style={styles.content}>
        {/* Restaurant Header */}
        <Card style={styles.restaurantHeader}>
          <View style={styles.restaurantImage}>
            <Text style={styles.restaurantEmoji}>🍛</Text>
          </View>
          
          <View style={styles.restaurantInfo}>
            <Text style={styles.restaurantName}>Karachi Biryani House</Text>
            <Text style={styles.restaurantCuisine}>Pakistani • Biryani</Text>
            
            <View style={styles.restaurantMeta}>
              <Text style={styles.rating}>⭐ 4.5</Text>
              <Text style={styles.deliveryTime}>25-35 min</Text>
              <Text style={styles.deliveryFee}>PKR 49 delivery</Text>
            </View>
          </View>
        </Card>
        
        {/* Menu Tabs */}
        <View style={styles.tabContainer}>
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            {tabs.map((tab) => (
              <TouchableOpacity
                key={tab.id}
                style={[
                  styles.tab,
                  selectedTab === tab.id && styles.tabActive,
                ]}
                onPress={() => setSelectedTab(tab.id)}
              >
                <Text
                  style={[
                    styles.tabText,
                    selectedTab === tab.id && styles.tabTextActive,
                  ]}
                >
                  {isUrdu ? tab.nameUrdu : tab.name}
                </Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>
        
        {/* Menu Items */}
        <View style={styles.menuContainer}>
          {menuData[selectedTab]?.map((item) => (
            <Card key={item.id} style={styles.menuItem} onPress={() => handleItemPress(item)}>
              <View style={styles.menuItemContent}>
                <View style={styles.menuItemInfo}>
                  <View style={styles.menuItemHeader}>
                    <Text style={styles.menuItemName}>
                      {isUrdu ? item.nameUrdu : item.name}
                    </Text>
                    {item.popular && (
                      <View style={styles.popularBadge}>
                        <Text style={styles.popularText}>Popular</Text>
                      </View>
                    )}
                  </View>

                  <Text style={styles.menuItemDescription}>
                    {isUrdu ? item.descriptionUrdu : item.description}
                  </Text>

                  <View style={styles.menuItemMeta}>
                    <Text style={[styles.vegIndicator, { color: item.isVeg ? Colors.success.main : Colors.error.main }]}>
                      {item.isVeg ? '🟢' : '🔴'}
                    </Text>
                    {item.spiceLevel && (
                      <Text style={styles.spiceLevel}>
                        🌶️ {item.spiceLevel}
                      </Text>
                    )}
                  </View>

                  <View style={styles.priceContainer}>
                    {item.originalPrice && (
                      <Text style={styles.originalPrice}>PKR {item.originalPrice}</Text>
                    )}
                    <Text style={styles.price}>PKR {item.price}</Text>
                    {item.discount && (
                      <View style={styles.discountBadge}>
                        <Text style={styles.discountText}>{item.discount}% OFF</Text>
                      </View>
                    )}
                  </View>
                </View>

                <View style={styles.menuItemImage}>
                  <Text style={styles.menuItemEmoji}>{item.image}</Text>
                  <TouchableOpacity
                    style={styles.addButton}
                    onPress={() => handleItemPress(item)}
                  >
                    <Text style={styles.addButtonText}>+</Text>
                  </TouchableOpacity>
                </View>
              </View>
            </Card>
          ))}
        </View>
      </ScrollView>

      {/* Cart Summary */}
      {cart.length > 0 && (
        <View style={styles.cartSummary}>
          <View style={styles.cartInfo}>
            <Text style={styles.cartItemCount}>{getCartItemCount()} items</Text>
            <Text style={styles.cartTotal}>PKR {getCartTotal()}</Text>
          </View>
          <Button
            title="View Cart"
            onPress={() => navigation.navigate('Cart', { cartItems: cart })}
            style={styles.viewCartButton}
          />
        </View>
      )}

      {/* Customization Modal */}
      <Modal
        visible={showCustomization}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setShowCustomization(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            {selectedItem && (
              <>
                <View style={styles.modalHeader}>
                  <Text style={styles.modalTitle}>
                    {isUrdu ? selectedItem.nameUrdu : selectedItem.name}
                  </Text>
                  <TouchableOpacity
                    style={styles.closeButton}
                    onPress={() => setShowCustomization(false)}
                  >
                    <Text style={styles.closeButtonText}>×</Text>
                  </TouchableOpacity>
                </View>

                <ScrollView style={styles.modalBody}>
                  {selectedItem.customizations?.map((customization) => (
                    <View key={customization.id} style={styles.customizationGroup}>
                      <Text style={styles.customizationTitle}>{customization.name}</Text>
                      {customization.options.map((option, index) => (
                        <TouchableOpacity
                          key={option}
                          style={[
                            styles.customizationOption,
                            customizations[customization.id] === option && styles.customizationOptionSelected,
                          ]}
                          onPress={() => setCustomizations(prev => ({
                            ...prev,
                            [customization.id]: option,
                          }))}
                        >
                          <Text style={[
                            styles.customizationOptionText,
                            customizations[customization.id] === option && styles.customizationOptionTextSelected,
                          ]}>
                            {option}
                            {customization.prices && customization.prices[index] > 0 &&
                              ` (+PKR ${customization.prices[index]})`
                            }
                          </Text>
                        </TouchableOpacity>
                      ))}
                    </View>
                  ))}

                  <View style={styles.quantityContainer}>
                    <Text style={styles.quantityLabel}>Quantity</Text>
                    <View style={styles.quantityControls}>
                      <TouchableOpacity
                        style={styles.quantityButton}
                        onPress={() => setQuantity(Math.max(1, quantity - 1))}
                      >
                        <Text style={styles.quantityButtonText}>-</Text>
                      </TouchableOpacity>
                      <Text style={styles.quantityText}>{quantity}</Text>
                      <TouchableOpacity
                        style={styles.quantityButton}
                        onPress={() => setQuantity(quantity + 1)}
                      >
                        <Text style={styles.quantityButtonText}>+</Text>
                      </TouchableOpacity>
                    </View>
                  </View>
                </ScrollView>

                <View style={styles.modalFooter}>
                  <Text style={styles.modalPrice}>
                    PKR {calculateItemPrice(selectedItem, customizations, quantity)}
                  </Text>
                  <Button
                    title="Add to Cart"
                    onPress={handleAddToCartWithCustomizations}
                    style={styles.addToCartButton}
                  />
                </View>
              </>
            )}
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background.primary,
  },
  
  content: {
    flex: 1,
  },
  
  restaurantHeader: {
    margin: Spacing.lg,
    padding: Spacing.lg,
  },
  
  restaurantImage: {
    height: 120,
    backgroundColor: Colors.background.secondary,
    borderRadius: Spacing.radius.md,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: Spacing.md,
  },
  
  restaurantEmoji: {
    fontSize: 48,
  },
  
  restaurantInfo: {
    alignItems: 'center',
  },
  
  restaurantName: {
    ...Typography.styles.h4,
    color: Colors.text.primary,
    marginBottom: Spacing.xs,
  },
  
  restaurantCuisine: {
    ...Typography.styles.body,
    color: Colors.text.secondary,
    marginBottom: Spacing.sm,
  },
  
  restaurantMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.md,
  },
  
  rating: {
    ...Typography.styles.labelSmall,
    color: Colors.success.main,
    backgroundColor: Colors.success.bg,
    paddingHorizontal: Spacing.xs,
    paddingVertical: 2,
    borderRadius: Spacing.radius.sm,
  },
  
  deliveryTime: {
    ...Typography.styles.caption,
    color: Colors.text.tertiary,
  },
  
  deliveryFee: {
    ...Typography.styles.caption,
    color: Colors.text.tertiary,
  },
  
  tabContainer: {
    borderBottomWidth: 1,
    borderBottomColor: Colors.border.light,
    paddingHorizontal: Spacing.lg,
  },
  
  tab: {
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
    marginRight: Spacing.md,
    borderBottomWidth: 2,
    borderBottomColor: 'transparent',
  },
  
  tabActive: {
    borderBottomColor: Colors.primary.main,
  },
  
  tabText: {
    ...Typography.styles.label,
    color: Colors.text.secondary,
  },
  
  tabTextActive: {
    color: Colors.primary.main,
    fontWeight: '600',
  },
  
  menuContainer: {
    paddingBottom: Spacing.xl,
  },

  menuItem: {
    marginHorizontal: Spacing.lg,
    marginBottom: Spacing.md,
    padding: Spacing.md,
  },

  menuItemContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },

  menuItemInfo: {
    flex: 1,
    marginRight: Spacing.md,
  },

  menuItemHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.xs,
  },

  menuItemName: {
    ...Typography.styles.bodyBold,
    color: Colors.text.primary,
    flex: 1,
  },

  popularBadge: {
    backgroundColor: Colors.accent.main,
    paddingHorizontal: Spacing.xs,
    paddingVertical: 2,
    borderRadius: Spacing.radius.sm,
  },

  popularText: {
    ...Typography.styles.caption,
    color: Colors.background.primary,
    fontSize: 10,
  },

  menuItemDescription: {
    ...Typography.styles.caption,
    color: Colors.text.secondary,
    marginBottom: Spacing.sm,
    lineHeight: 16,
  },

  menuItemMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.sm,
  },

  vegIndicator: {
    fontSize: 12,
    marginRight: Spacing.xs,
  },

  spiceLevel: {
    ...Typography.styles.caption,
    color: Colors.text.secondary,
    fontSize: 11,
  },

  priceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },

  originalPrice: {
    ...Typography.styles.caption,
    color: Colors.text.secondary,
    textDecorationLine: 'line-through',
    marginRight: Spacing.xs,
  },

  price: {
    ...Typography.styles.bodyBold,
    color: Colors.text.primary,
    marginRight: Spacing.xs,
  },

  discountBadge: {
    backgroundColor: Colors.success.main,
    paddingHorizontal: Spacing.xs,
    paddingVertical: 1,
    borderRadius: Spacing.radius.sm,
  },

  discountText: {
    ...Typography.styles.caption,
    color: Colors.background.primary,
    fontSize: 10,
  },

  menuItemImage: {
    alignItems: 'center',
    position: 'relative',
  },

  menuItemEmoji: {
    fontSize: 40,
    marginBottom: Spacing.xs,
  },

  addButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: Colors.primary.main,
    alignItems: 'center',
    justifyContent: 'center',
  },

  addButtonText: {
    ...Typography.styles.bodyBold,
    color: Colors.background.primary,
    fontSize: 18,
  },

  // Cart Summary Styles
  cartSummary: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: Colors.primary.main,
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.md,
    borderTopLeftRadius: Spacing.radius.lg,
    borderTopRightRadius: Spacing.radius.lg,
  },

  cartInfo: {
    flex: 1,
  },

  cartItemCount: {
    ...Typography.styles.caption,
    color: Colors.background.primary,
  },

  cartTotal: {
    ...Typography.styles.bodyBold,
    color: Colors.background.primary,
    fontSize: 18,
  },

  viewCartButton: {
    backgroundColor: Colors.background.primary,
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.sm,
  },

  // Modal Styles
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },

  modalContent: {
    backgroundColor: Colors.background.primary,
    borderTopLeftRadius: Spacing.radius.xl,
    borderTopRightRadius: Spacing.radius.xl,
    maxHeight: '80%',
  },

  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: Spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border.light,
  },

  modalTitle: {
    ...Typography.styles.h5,
    color: Colors.text.primary,
    flex: 1,
  },

  closeButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: Colors.background.secondary,
    alignItems: 'center',
    justifyContent: 'center',
  },

  closeButtonText: {
    fontSize: 20,
    color: Colors.text.secondary,
  },

  modalBody: {
    padding: Spacing.lg,
  },

  customizationGroup: {
    marginBottom: Spacing.lg,
  },

  customizationTitle: {
    ...Typography.styles.bodyBold,
    color: Colors.text.primary,
    marginBottom: Spacing.sm,
  },

  customizationOption: {
    paddingVertical: Spacing.sm,
    paddingHorizontal: Spacing.md,
    borderWidth: 1,
    borderColor: Colors.border.light,
    borderRadius: Spacing.radius.md,
    marginBottom: Spacing.xs,
  },

  customizationOptionSelected: {
    borderColor: Colors.primary.main,
    backgroundColor: Colors.primary.main + '10',
  },

  customizationOptionText: {
    ...Typography.styles.body,
    color: Colors.text.secondary,
  },

  customizationOptionTextSelected: {
    color: Colors.primary.main,
    fontWeight: '600',
  },

  quantityContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginTop: Spacing.lg,
  },

  quantityLabel: {
    ...Typography.styles.bodyBold,
    color: Colors.text.primary,
  },

  quantityControls: {
    flexDirection: 'row',
    alignItems: 'center',
  },

  quantityButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: Colors.background.secondary,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: Colors.border.light,
  },

  quantityButtonText: {
    ...Typography.styles.bodyBold,
    color: Colors.text.primary,
    fontSize: 18,
  },

  quantityText: {
    ...Typography.styles.bodyBold,
    color: Colors.text.primary,
    marginHorizontal: Spacing.lg,
    fontSize: 16,
  },

  modalFooter: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: Spacing.lg,
    borderTopWidth: 1,
    borderTopColor: Colors.border.light,
  },

  modalPrice: {
    ...Typography.styles.h5,
    color: Colors.text.primary,
  },

  addToCartButton: {
    flex: 1,
    marginLeft: Spacing.lg,
  },
});

export default RestaurantScreen;
