import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  StatusBar,
} from 'react-native';
import { Colors, Typography, Spacing } from '../constants';
import Input from '../components/common/Input';
import Card from '../components/common/Card';

const SearchScreen = ({ navigation, route }) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState(route?.params?.category || null);
  
  return (
    <View style={styles.container}>
      <StatusBar
        barStyle="dark-content"
        backgroundColor={Colors.background.primary}
      />
      
      <View style={styles.header}>
        <Input
          placeholder="Search for restaurants or food..."
          value={searchQuery}
          onChangeText={setSearchQuery}
          leftIcon={<Text style={styles.searchIcon}>🔍</Text>}
          style={styles.searchInput}
        />
      </View>
      
      <ScrollView style={styles.content}>
        <Card style={styles.placeholderCard}>
          <Text style={styles.placeholderTitle}>Search Screen</Text>
          <Text style={styles.placeholderText}>
            This screen will contain:
            {'\n'}• Search functionality
            {'\n'}• Filter options
            {'\n'}• Restaurant listings
            {'\n'}• Food item search results
          </Text>
          {selectedCategory && (
            <Text style={styles.categoryText}>
              Category: {selectedCategory}
            </Text>
          )}
        </Card>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background.primary,
  },
  
  header: {
    paddingHorizontal: Spacing.lg,
    paddingTop: Spacing.lg,
    paddingBottom: Spacing.md,
  },
  
  searchInput: {
    marginBottom: 0,
  },
  
  searchIcon: {
    fontSize: 16,
  },
  
  content: {
    flex: 1,
    paddingHorizontal: Spacing.lg,
  },
  
  placeholderCard: {
    padding: Spacing.lg,
    alignItems: 'center',
  },
  
  placeholderTitle: {
    ...Typography.styles.h4,
    color: Colors.text.primary,
    marginBottom: Spacing.md,
  },
  
  placeholderText: {
    ...Typography.styles.body,
    color: Colors.text.secondary,
    textAlign: 'center',
    lineHeight: 24,
  },
  
  categoryText: {
    ...Typography.styles.label,
    color: Colors.primary.main,
    marginTop: Spacing.md,
  },
});

export default SearchScreen;
