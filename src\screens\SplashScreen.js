import React, { useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Animated,
  Dimensions,
  StatusBar,
} from 'react-native';
import { Colors, Typography, Spacing } from '../constants';

const { width, height } = Dimensions.get('window');

const SplashScreen = ({ navigation }) => {
  const logoScale = useRef(new Animated.Value(0.5)).current;
  const logoOpacity = useRef(new Animated.Value(0)).current;
  const textOpacity = useRef(new Animated.Value(0)).current;
  const backgroundOpacity = useRef(new Animated.Value(0)).current;
  
  useEffect(() => {
    // Start animations
    const animateSequence = () => {
      // Background fade in
      Animated.timing(backgroundOpacity, {
        toValue: 1,
        duration: 500,
        useNativeDriver: true,
      }).start();
      
      // Logo animation
      Animated.parallel([
        Animated.timing(logoOpacity, {
          toValue: 1,
          duration: 800,
          useNativeDriver: true,
        }),
        Animated.spring(logoScale, {
          toValue: 1,
          tension: 50,
          friction: 7,
          useNativeDriver: true,
        }),
      ]).start(() => {
        // Text fade in after logo
        Animated.timing(textOpacity, {
          toValue: 1,
          duration: 600,
          useNativeDriver: true,
        }).start(() => {
          // Navigate after animations complete
          setTimeout(() => {
            navigation.replace('MainTabs'); // or 'Onboarding' for first launch
          }, 1000);
        });
      });
    };
    
    animateSequence();
  }, []);
  
  return (
    <View style={styles.container}>
      <StatusBar
        barStyle="light-content"
        backgroundColor={Colors.primary.main}
        translucent={false}
      />
      
      {/* Animated Background */}
      <Animated.View
        style={[
          styles.backgroundGradient,
          { opacity: backgroundOpacity }
        ]}
      />
      
      {/* Logo Container */}
      <View style={styles.logoContainer}>
        <Animated.View
          style={[
            styles.logoWrapper,
            {
              opacity: logoOpacity,
              transform: [{ scale: logoScale }],
            },
          ]}
        >
          {/* App Logo - Using emoji for now, replace with actual logo */}
          <View style={styles.logo}>
            <Text style={styles.logoEmoji}>🍽️</Text>
          </View>
          
          <Animated.Text
            style={[
              styles.appName,
              { opacity: textOpacity }
            ]}
          >
            FoodieExpress
          </Animated.Text>
          
          <Animated.Text
            style={[
              styles.tagline,
              { opacity: textOpacity }
            ]}
          >
            Fast • Fresh • Delicious
          </Animated.Text>
        </Animated.View>
      </View>
      
      {/* Bottom Section */}
      <Animated.View
        style={[
          styles.bottomSection,
          { opacity: textOpacity }
        ]}
      >
        <Text style={styles.loadingText}>
          Loading delicious food...
        </Text>
        
        {/* Loading indicator */}
        <View style={styles.loadingIndicator}>
          <View style={styles.loadingDot} />
          <View style={[styles.loadingDot, styles.loadingDotDelay1]} />
          <View style={[styles.loadingDot, styles.loadingDotDelay2]} />
        </View>
      </Animated.View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.primary.main,
    justifyContent: 'center',
    alignItems: 'center',
  },
  
  backgroundGradient: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: Colors.primary.main,
    // Add gradient background if needed
  },
  
  logoContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: Spacing.xl,
  },
  
  logoWrapper: {
    alignItems: 'center',
  },
  
  logo: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: Colors.neutral.white,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: Spacing.lg,
    ...Spacing.shadow.lg,
  },
  
  logoEmoji: {
    fontSize: 60,
  },
  
  appName: {
    ...Typography.styles.h1,
    color: Colors.neutral.white,
    textAlign: 'center',
    marginBottom: Spacing.sm,
    fontWeight: '800',
  },
  
  tagline: {
    ...Typography.styles.body,
    color: Colors.primary[100],
    textAlign: 'center',
    letterSpacing: 1,
  },
  
  bottomSection: {
    position: 'absolute',
    bottom: 80,
    alignItems: 'center',
  },
  
  loadingText: {
    ...Typography.styles.bodySmall,
    color: Colors.primary[100],
    marginBottom: Spacing.md,
  },
  
  loadingIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.xs,
  },
  
  loadingDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: Colors.neutral.white,
    opacity: 0.6,
  },
  
  loadingDotDelay1: {
    // Add animation delay if needed
  },
  
  loadingDotDelay2: {
    // Add animation delay if needed
  },
});

export default SplashScreen;
