import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  StatusBar,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { Colors, Typography, Spacing, AppConfig } from '../../constants';
import Button from '../../components/common/Button';
import Input from '../../components/common/Input';
import Card from '../../components/common/Card';

const LoginScreen = ({ navigation }) => {
  const [phoneNumber, setPhoneNumber] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [loginMethod, setLoginMethod] = useState('phone'); // 'phone' or 'email'
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState({});
  
  const validateForm = () => {
    const newErrors = {};
    
    if (loginMethod === 'phone') {
      if (!phoneNumber.trim()) {
        newErrors.phoneNumber = 'Phone number is required';
      } else if (!/^(\+92|0)?[0-9]{10}$/.test(phoneNumber.replace(/\s/g, ''))) {
        newErrors.phoneNumber = 'Please enter a valid Pakistani phone number';
      }
    } else {
      if (!email.trim()) {
        newErrors.email = 'Email is required';
      } else if (!/\S+@\S+\.\S+/.test(email)) {
        newErrors.email = 'Please enter a valid email address';
      }
    }
    
    // Password is optional for now (OTP-based login)
    if (password && password.length < 6) {
      newErrors.password = 'Password must be at least 6 characters';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };
  
  const handleLogin = async () => {
    if (!validateForm()) return;
    
    setLoading(true);
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Navigate to main app
      navigation.replace('MainTabs');
    } catch (error) {
      console.error('Login error:', error);
    } finally {
      setLoading(false);
    }
  };
  
  const handleGuestLogin = () => {
    navigation.replace('MainTabs');
  };
  
  const handleSignup = () => {
    navigation.navigate('Signup');
  };
  
  const formatPhoneNumber = (text) => {
    // Format Pakistani phone number
    const cleaned = text.replace(/\D/g, '');
    const match = cleaned.match(/^(\d{0,4})(\d{0,7})$/);
    if (match) {
      return match[1] + (match[2] ? ' ' + match[2] : '');
    }
    return text;
  };
  
  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <StatusBar
        barStyle="dark-content"
        backgroundColor={Colors.background.primary}
      />
      
      <ScrollView
        contentContainerStyle={styles.scrollContent}
        keyboardShouldPersistTaps="handled"
        showsVerticalScrollIndicator={false}
      >
        {/* Header */}
        <View style={styles.header}>
          <View style={styles.logoContainer}>
            <Text style={styles.logoEmoji}>🍽️</Text>
          </View>
          <Text style={styles.title}>Welcome Back!</Text>
          <Text style={styles.subtitle}>
            Sign in to continue ordering delicious food
          </Text>
        </View>
        
        {/* Login Form */}
        <Card style={styles.formCard} padding="large">
          {/* Login Method Toggle */}
          <View style={styles.toggleContainer}>
            <TouchableOpacity
              style={[
                styles.toggleButton,
                loginMethod === 'phone' && styles.toggleButtonActive,
              ]}
              onPress={() => setLoginMethod('phone')}
            >
              <Text
                style={[
                  styles.toggleButtonText,
                  loginMethod === 'phone' && styles.toggleButtonTextActive,
                ]}
              >
                📱 Phone
              </Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[
                styles.toggleButton,
                loginMethod === 'email' && styles.toggleButtonActive,
              ]}
              onPress={() => setLoginMethod('email')}
            >
              <Text
                style={[
                  styles.toggleButtonText,
                  loginMethod === 'email' && styles.toggleButtonTextActive,
                ]}
              >
                ✉️ Email
              </Text>
            </TouchableOpacity>
          </View>
          
          {/* Input Fields */}
          {loginMethod === 'phone' ? (
            <Input
              label="Phone Number"
              placeholder="03XX XXXXXXX"
              value={phoneNumber}
              onChangeText={(text) => setPhoneNumber(formatPhoneNumber(text))}
              keyboardType="phone-pad"
              maxLength={12}
              error={errors.phoneNumber}
              leftIcon={<Text style={styles.inputIcon}>🇵🇰</Text>}
            />
          ) : (
            <Input
              label="Email Address"
              placeholder="<EMAIL>"
              value={email}
              onChangeText={setEmail}
              keyboardType="email-address"
              autoCapitalize="none"
              error={errors.email}
              leftIcon={<Text style={styles.inputIcon}>✉️</Text>}
            />
          )}
          
          {/* Optional Password Field */}
          {AppConfig.features.otpVerification === false && (
            <Input
              label="Password (Optional)"
              placeholder="Enter your password"
              value={password}
              onChangeText={setPassword}
              secureTextEntry
              error={errors.password}
              helperText="Leave empty to receive OTP instead"
              leftIcon={<Text style={styles.inputIcon}>🔒</Text>}
            />
          )}
          
          {/* Login Button */}
          <Button
            title={loading ? 'Signing In...' : 'Sign In'}
            onPress={handleLogin}
            loading={loading}
            fullWidth
            style={styles.loginButton}
          />
          
          {/* Divider */}
          <View style={styles.divider}>
            <View style={styles.dividerLine} />
            <Text style={styles.dividerText}>or</Text>
            <View style={styles.dividerLine} />
          </View>
          
          {/* Guest Login */}
          {AppConfig.features.guestMode && (
            <Button
              title="Continue as Guest"
              onPress={handleGuestLogin}
              variant="outline"
              fullWidth
              style={styles.guestButton}
            />
          )}
        </Card>
        
        {/* Footer */}
        <View style={styles.footer}>
          <View style={styles.footerTextContainer}>
            <Text style={styles.footerText}>Don't have an account? </Text>
            <TouchableOpacity onPress={handleSignup}>
              <Text style={styles.footerLink}>Sign Up</Text>
            </TouchableOpacity>
          </View>
          
          <Text style={styles.termsText}>
            By continuing, you agree to our{' '}
            <Text style={styles.termsLink}>Terms of Service</Text>
            {' '}and{' '}
            <Text style={styles.termsLink}>Privacy Policy</Text>
          </Text>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background.primary,
  },
  
  scrollContent: {
    flexGrow: 1,
    paddingHorizontal: Spacing.lg,
    paddingTop: Spacing.xl,
    paddingBottom: Spacing.lg,
  },
  
  header: {
    alignItems: 'center',
    marginBottom: Spacing.xl,
  },
  
  logoContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: Colors.primary.main,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: Spacing.lg,
    ...Spacing.shadow.md,
  },
  
  logoEmoji: {
    fontSize: 40,
  },
  
  title: {
    ...Typography.styles.h2,
    color: Colors.text.primary,
    marginBottom: Spacing.sm,
  },
  
  subtitle: {
    ...Typography.styles.body,
    color: Colors.text.secondary,
    textAlign: 'center',
  },
  
  formCard: {
    marginBottom: Spacing.xl,
  },
  
  toggleContainer: {
    flexDirection: 'row',
    backgroundColor: Colors.background.secondary,
    borderRadius: Spacing.radius.md,
    padding: 4,
    marginBottom: Spacing.lg,
  },
  
  toggleButton: {
    flex: 1,
    paddingVertical: Spacing.sm,
    alignItems: 'center',
    borderRadius: Spacing.radius.sm,
  },
  
  toggleButtonActive: {
    backgroundColor: Colors.primary.main,
  },
  
  toggleButtonText: {
    ...Typography.styles.label,
    color: Colors.text.secondary,
  },
  
  toggleButtonTextActive: {
    color: Colors.neutral.white,
    fontWeight: '600',
  },
  
  inputIcon: {
    fontSize: 16,
  },
  
  loginButton: {
    marginTop: Spacing.md,
    marginBottom: Spacing.lg,
  },
  
  divider: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.lg,
  },
  
  dividerLine: {
    flex: 1,
    height: 1,
    backgroundColor: Colors.border.light,
  },
  
  dividerText: {
    ...Typography.styles.bodySmall,
    color: Colors.text.tertiary,
    marginHorizontal: Spacing.md,
  },
  
  guestButton: {
    marginBottom: Spacing.sm,
  },
  
  footer: {
    alignItems: 'center',
    gap: Spacing.lg,
  },

  footerTextContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },

  footerText: {
    ...Typography.styles.body,
    color: Colors.text.secondary,
  },
  
  footerLink: {
    ...Typography.styles.body,
    color: Colors.primary.main,
    fontWeight: '600',
  },
  
  termsText: {
    ...Typography.styles.caption,
    color: Colors.text.tertiary,
    textAlign: 'center',
    lineHeight: 18,
  },
  
  termsLink: {
    color: Colors.primary.main,
    fontWeight: '500',
  },
});

export default LoginScreen;
