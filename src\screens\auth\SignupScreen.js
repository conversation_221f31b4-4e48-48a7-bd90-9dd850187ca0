import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  StatusBar,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { Colors, Typography, Spacing, AppConfig } from '../../constants';
import Button from '../../components/common/Button';
import Input from '../../components/common/Input';
import Card from '../../components/common/Card';

const SignupScreen = ({ navigation }) => {
  const [formData, setFormData] = useState({
    name: '',
    phoneNumber: '',
    email: '',
    password: '',
    confirmPassword: '',
  });
  const [signupMethod, setSignupMethod] = useState('phone'); // 'phone' or 'email'
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState({});
  const [acceptTerms, setAcceptTerms] = useState(false);
  
  const updateFormData = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: null }));
    }
  };
  
  const validateForm = () => {
    const newErrors = {};
    
    // Name validation
    if (!formData.name.trim()) {
      newErrors.name = 'Full name is required';
    } else if (formData.name.trim().length < 2) {
      newErrors.name = 'Name must be at least 2 characters';
    }
    
    // Phone/Email validation
    if (signupMethod === 'phone') {
      if (!formData.phoneNumber.trim()) {
        newErrors.phoneNumber = 'Phone number is required';
      } else if (!/^(\+92|0)?[0-9]{10}$/.test(formData.phoneNumber.replace(/\s/g, ''))) {
        newErrors.phoneNumber = 'Please enter a valid Pakistani phone number';
      }
    } else {
      if (!formData.email.trim()) {
        newErrors.email = 'Email is required';
      } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
        newErrors.email = 'Please enter a valid email address';
      }
    }
    
    // Password validation (only if not using OTP)
    if (!AppConfig.features.otpVerification) {
      if (!formData.password) {
        newErrors.password = 'Password is required';
      } else if (formData.password.length < 6) {
        newErrors.password = 'Password must be at least 6 characters';
      }
      
      if (formData.password !== formData.confirmPassword) {
        newErrors.confirmPassword = 'Passwords do not match';
      }
    }
    
    // Terms acceptance
    if (!acceptTerms) {
      newErrors.terms = 'Please accept the terms and conditions';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };
  
  const handleSignup = async () => {
    if (!validateForm()) return;
    
    setLoading(true);
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Navigate to main app or verification screen
      navigation.replace('MainTabs');
    } catch (error) {
      console.error('Signup error:', error);
    } finally {
      setLoading(false);
    }
  };
  
  const handleLogin = () => {
    navigation.navigate('Login');
  };
  
  const formatPhoneNumber = (text) => {
    const cleaned = text.replace(/\D/g, '');
    const match = cleaned.match(/^(\d{0,4})(\d{0,7})$/);
    if (match) {
      return match[1] + (match[2] ? ' ' + match[2] : '');
    }
    return text;
  };
  
  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <StatusBar
        barStyle="dark-content"
        backgroundColor={Colors.background.primary}
      />
      
      <ScrollView
        contentContainerStyle={styles.scrollContent}
        keyboardShouldPersistTaps="handled"
        showsVerticalScrollIndicator={false}
      >
        {/* Header */}
        <View style={styles.header}>
          <View style={styles.logoContainer}>
            <Text style={styles.logoEmoji}>🍽️</Text>
          </View>
          <Text style={styles.title}>Create Account</Text>
          <Text style={styles.subtitle}>
            Join us and start ordering delicious food
          </Text>
        </View>
        
        {/* Signup Form */}
        <Card style={styles.formCard} padding="large">
          {/* Name Field */}
          <Input
            label="Full Name"
            placeholder="Enter your full name"
            value={formData.name}
            onChangeText={(text) => updateFormData('name', text)}
            error={errors.name}
            leftIcon={<Text style={styles.inputIcon}>👤</Text>}
          />
          
          {/* Signup Method Toggle */}
          <View style={styles.toggleContainer}>
            <TouchableOpacity
              style={[
                styles.toggleButton,
                signupMethod === 'phone' && styles.toggleButtonActive,
              ]}
              onPress={() => setSignupMethod('phone')}
            >
              <Text
                style={[
                  styles.toggleButtonText,
                  signupMethod === 'phone' && styles.toggleButtonTextActive,
                ]}
              >
                📱 Phone
              </Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[
                styles.toggleButton,
                signupMethod === 'email' && styles.toggleButtonActive,
              ]}
              onPress={() => setSignupMethod('email')}
            >
              <Text
                style={[
                  styles.toggleButtonText,
                  signupMethod === 'email' && styles.toggleButtonTextActive,
                ]}
              >
                ✉️ Email
              </Text>
            </TouchableOpacity>
          </View>
          
          {/* Contact Field */}
          {signupMethod === 'phone' ? (
            <Input
              label="Phone Number"
              placeholder="03XX XXXXXXX"
              value={formData.phoneNumber}
              onChangeText={(text) => updateFormData('phoneNumber', formatPhoneNumber(text))}
              keyboardType="phone-pad"
              maxLength={12}
              error={errors.phoneNumber}
              leftIcon={<Text style={styles.inputIcon}>🇵🇰</Text>}
            />
          ) : (
            <Input
              label="Email Address"
              placeholder="<EMAIL>"
              value={formData.email}
              onChangeText={(text) => updateFormData('email', text)}
              keyboardType="email-address"
              autoCapitalize="none"
              error={errors.email}
              leftIcon={<Text style={styles.inputIcon}>✉️</Text>}
            />
          )}
          
          {/* Password Fields (only if not using OTP) */}
          {!AppConfig.features.otpVerification && (
            <>
              <Input
                label="Password"
                placeholder="Create a password"
                value={formData.password}
                onChangeText={(text) => updateFormData('password', text)}
                secureTextEntry
                error={errors.password}
                leftIcon={<Text style={styles.inputIcon}>🔒</Text>}
              />
              
              <Input
                label="Confirm Password"
                placeholder="Confirm your password"
                value={formData.confirmPassword}
                onChangeText={(text) => updateFormData('confirmPassword', text)}
                secureTextEntry
                error={errors.confirmPassword}
                leftIcon={<Text style={styles.inputIcon}>🔒</Text>}
              />
            </>
          )}
          
          {/* Terms and Conditions */}
          <TouchableOpacity
            style={styles.termsContainer}
            onPress={() => setAcceptTerms(!acceptTerms)}
          >
            <View style={[styles.checkbox, acceptTerms && styles.checkboxActive]}>
              {acceptTerms && <Text style={styles.checkmark}>✓</Text>}
            </View>
            <Text style={styles.termsText}>
              I agree to the{' '}
              <Text style={styles.termsLink}>Terms of Service</Text>
              {' '}and{' '}
              <Text style={styles.termsLink}>Privacy Policy</Text>
            </Text>
          </TouchableOpacity>
          
          {errors.terms && (
            <Text style={styles.errorText}>{errors.terms}</Text>
          )}
          
          {/* Signup Button */}
          <Button
            title={loading ? 'Creating Account...' : 'Create Account'}
            onPress={handleSignup}
            loading={loading}
            fullWidth
            style={styles.signupButton}
          />
        </Card>
        
        {/* Footer */}
        <View style={styles.footer}>
          <View style={styles.footerTextContainer}>
            <Text style={styles.footerText}>Already have an account? </Text>
            <TouchableOpacity onPress={handleLogin}>
              <Text style={styles.footerLink}>Sign In</Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background.primary,
  },

  scrollContent: {
    flexGrow: 1,
    paddingHorizontal: Spacing.lg,
    paddingTop: Spacing.xl,
    paddingBottom: Spacing.lg,
  },

  header: {
    alignItems: 'center',
    marginBottom: Spacing.xl,
  },

  logoContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: Colors.primary.main,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: Spacing.lg,
    ...Spacing.shadow.md,
  },

  logoEmoji: {
    fontSize: 40,
  },

  title: {
    ...Typography.styles.h2,
    color: Colors.text.primary,
    marginBottom: Spacing.sm,
  },

  subtitle: {
    ...Typography.styles.body,
    color: Colors.text.secondary,
    textAlign: 'center',
  },

  formCard: {
    marginBottom: Spacing.xl,
  },

  inputIcon: {
    fontSize: 16,
  },

  toggleContainer: {
    flexDirection: 'row',
    backgroundColor: Colors.background.secondary,
    borderRadius: Spacing.radius.md,
    padding: 4,
    marginBottom: Spacing.lg,
  },

  toggleButton: {
    flex: 1,
    paddingVertical: Spacing.sm,
    alignItems: 'center',
    borderRadius: Spacing.radius.sm,
  },

  toggleButtonActive: {
    backgroundColor: Colors.primary.main,
  },

  toggleButtonText: {
    ...Typography.styles.label,
    color: Colors.text.secondary,
  },

  toggleButtonTextActive: {
    color: Colors.neutral.white,
    fontWeight: '600',
  },

  termsContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: Spacing.md,
    gap: Spacing.sm,
  },

  checkbox: {
    width: 20,
    height: 20,
    borderWidth: 2,
    borderColor: Colors.border.medium,
    borderRadius: 4,
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 2,
  },

  checkboxActive: {
    backgroundColor: Colors.primary.main,
    borderColor: Colors.primary.main,
  },

  checkmark: {
    color: Colors.neutral.white,
    fontSize: 12,
    fontWeight: 'bold',
  },

  termsText: {
    ...Typography.styles.bodySmall,
    color: Colors.text.secondary,
    flex: 1,
    lineHeight: 20,
  },

  termsLink: {
    color: Colors.primary.main,
    fontWeight: '500',
  },

  errorText: {
    ...Typography.styles.caption,
    color: Colors.error.main,
    marginBottom: Spacing.sm,
  },

  signupButton: {
    marginTop: Spacing.md,
  },

  footer: {
    alignItems: 'center',
  },

  footerTextContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },

  footerText: {
    ...Typography.styles.body,
    color: Colors.text.secondary,
  },

  footerLink: {
    ...Typography.styles.body,
    color: Colors.primary.main,
    fontWeight: '600',
  },
});

export default SignupScreen;
